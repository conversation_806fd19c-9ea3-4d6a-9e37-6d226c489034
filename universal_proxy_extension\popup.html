<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .section {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      backdrop-filter: blur(10px);
    }
    
    .section h3 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #e0e0e0;
    }
    
    .ip-display {
      background: rgba(0, 0, 0, 0.2);
      padding: 10px;
      border-radius: 5px;
      text-align: center;
      font-family: 'Courier New', monospace;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 12px;
      margin: 5px;
      transition: all 0.3s ease;
    }
    
    .button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
    
    .button:active {
      transform: translateY(0);
    }
    
    .button.primary {
      background: #4CAF50;
      border-color: #45a049;
    }
    
    .button.danger {
      background: #f44336;
      border-color: #da190b;
    }
    
    .input-group {
      margin-bottom: 10px;
    }
    
    .input-group label {
      display: block;
      font-size: 12px;
      margin-bottom: 5px;
      color: #e0e0e0;
    }
    
    .input-group input {
      width: 100%;
      padding: 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 12px;
      box-sizing: border-box;
    }
    
    .input-group input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .status {
      font-size: 11px;
      padding: 5px;
      border-radius: 3px;
      margin-top: 10px;
      text-align: center;
    }
    
    .status.success {
      background: rgba(76, 175, 80, 0.3);
      border: 1px solid #4CAF50;
    }
    
    .status.error {
      background: rgba(244, 67, 54, 0.3);
      border: 1px solid #f44336;
    }
    
    .proxy-buttons {
      display: flex;
      gap: 5px;
    }
    
    .proxy-buttons .button {
      flex: 1;
      margin: 0;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🚀 DataImpulse Proxy</h1>
  </div>

  <div class="section">
    <h3>🌐 当前IP地址</h3>
    <div class="ip-display" id="currentIP">点击测试IP</div>
    <button class="button primary" id="testIPBtn">🔍 测试IP</button>
  </div>

  <div class="section">
    <h3>⚡ 快速代理切换</h3>
    <div class="input-group">
      <label>用户名:</label>
      <input type="text" id="username" placeholder="DataImpulse用户名">
    </div>
    <div class="input-group">
      <label>密码:</label>
      <input type="password" id="password" placeholder="DataImpulse密码">
    </div>
    <div class="proxy-buttons">
      <button class="button" id="proxy1Btn">代理1 (19965)</button>
      <button class="button" id="proxy2Btn">代理2 (19966)</button>
    </div>
    <button class="button danger" id="disableBtn">🚫 禁用代理</button>
  </div>

  <div class="section">
    <h3>📊 状态信息</h3>
    <div id="status" class="status">准备就绪</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
