#!/usr/bin/env python3
"""
API数据模型定义
支持POST JSON格式的请求和响应，包含代理配置和验证
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, Union
from enum import Enum

class ProxyScheme(str, Enum):
    """代理协议类型"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS5 = "socks5"

class ProxyConfig(BaseModel):
    """代理配置对象"""
    scheme: ProxyScheme = Field(..., description="代理协议类型")
    host: str = Field(..., description="代理服务器地址")
    port: int = Field(..., description="代理服务器端口", ge=1, le=65535)
    username: Optional[str] = Field(None, description="代理用户名（可选）")
    password: Optional[str] = Field(None, description="代理密码（可选）")
    
    def to_url(self) -> str:
        """转换为URL格式的代理字符串"""
        if self.username and self.password:
            return f"{self.scheme}://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            return f"{self.scheme}://{self.host}:{self.port}"

class RequestType(str, Enum):
    """请求类型"""
    COOKIES = "cookies"
    HTML = "html"

class CookiesRequest(BaseModel):
    """Cookies API请求模型"""
    authToken: str = Field(..., description="认证令牌")
    type: RequestType = Field(RequestType.COOKIES, description="请求类型")
    url: str = Field(..., description="目标URL")
    userAgent: Optional[str] = Field(None, description="用户代理字符串")
    proxy: Optional[ProxyConfig] = Field(None, description="代理配置")
    content: bool = Field(False, description="是否同时返回HTML内容")
    retries: int = Field(5, description="重试次数", ge=1, le=10)

class CookiesResponse(BaseModel):
    """Cookies API响应模型"""
    cookies: Dict[str, str] = Field(..., description="获取到的cookies")
    user_agent: str = Field(..., description="实际使用的用户代理")
    type: RequestType = Field(..., description="响应类型")
    html: Optional[str] = Field(None, description="HTML内容（当content=true时包含）")
    proxy_used: Optional[str] = Field(None, description="实际使用的代理地址")
    success: bool = Field(True, description="请求是否成功")
    message: Optional[str] = Field(None, description="附加信息或错误消息")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误详细信息")
    code: int = Field(..., description="错误代码")

# 工具函数
def convert_old_proxy_format(proxy_str: str) -> Optional[ProxyConfig]:
    """
    将旧格式的代理字符串转换为新的ProxyConfig对象
    
    Args:
        proxy_str: 旧格式代理字符串，如 "*********************:port"
        
    Returns:
        ProxyConfig对象或None
    """
    if not proxy_str:
        return None
    
    try:
        from urllib.parse import urlparse
        parsed = urlparse(proxy_str)
        
        return ProxyConfig(
            scheme=parsed.scheme,
            host=parsed.hostname,
            port=parsed.port,
            username=parsed.username,
            password=parsed.password
        )
    except Exception:
        return None

def validate_auth_token(token: str, expected_token: str) -> bool:
    """
    验证认证令牌
    
    Args:
        token: 提供的令牌
        expected_token: 期望的令牌
        
    Returns:
        是否验证通过
    """
    return token == expected_token

# 示例用法
if __name__ == "__main__":
    # 测试代理配置
    proxy_config = ProxyConfig(
        scheme=ProxyScheme.SOCKS5,
        host="gw.dataimpulse.com",
        port=19965,
        username="0465846b31e1f583b17c__cr.us",
        password="16af34bf75f0573a"
    )
    
    print("代理URL:", proxy_config.to_url())
    
    # 测试请求模型
    request = CookiesRequest(
        authToken="gua12345",
        type=RequestType.COOKIES,
        url="https://example.com",
        userAgent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        proxy=proxy_config,
        content=True,
        retries=3
    )
    
    print("请求JSON:", request.model_dump_json(indent=2))
    
    # 测试响应模型
    response = CookiesResponse(
        cookies={"cf_clearance": "example_value", "session": "abc123"},
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        type=RequestType.COOKIES,
        html="<html><body>Example</body></html>",
        proxy_used="http://127.0.0.1:12345",
        success=True,
        message="Successfully bypassed Cloudflare"
    )
    
    print("响应JSON:", response.model_dump_json(indent=2))
