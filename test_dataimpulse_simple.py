"""
DataImpulse代理简单测试脚本
用于快速验证扩展是否正常工作

使用前请修改用户名和密码
"""

import time
from dataimpulse_browser import DataImpulseBrowser


def main():
    print("🚀 DataImpulse代理简单测试")
    print("=" * 40)
    
    # ⚠️ 请替换为你的DataImpulse凭据
    USERNAME = "your_username"  # 替换为你的用户名
    PASSWORD = "your_password"  # 替换为你的密码
    
    if USERNAME == "your_username" or PASSWORD == "your_password":
        print("❌ 请先设置你的DataImpulse用户名和密码！")
        print("   在脚本中修改 USERNAME 和 PASSWORD 变量")
        print("\n💡 示例:")
        print('   USERNAME = "你的用户名"')
        print('   PASSWORD = "你的密码"')
        return
    
    print(f"📋 配置信息:")
    print(f"   用户名: {USERNAME}")
    print(f"   密码: {'*' * len(PASSWORD)}")
    print(f"   代理1: gw.dataimpulse.com:19965")
    print(f"   代理2: gw.dataimpulse.com:19966")
    print()
    
    try:
        # 创建浏览器
        print("🔧 启动浏览器...")
        browser = DataImpulseBrowser(USERNAME, PASSWORD)
        
        # 测试当前IP
        print("\n🌐 测试当前IP:")
        current_ip = browser.test_ip()
        
        # 测试代理1
        print("\n⚡ 切换到代理1 (19965):")
        if browser.switch_to_proxy1():
            time.sleep(2)
            proxy1_ip = browser.test_ip()
            print(f"✅ 代理1 IP: {proxy1_ip}")
        
        # 测试代理2
        print("\n⚡ 切换到代理2 (19966):")
        if browser.switch_to_proxy2():
            time.sleep(2)
            proxy2_ip = browser.test_ip()
            print(f"✅ 代理2 IP: {proxy2_ip}")
        
        # 禁用代理
        print("\n🚫 禁用代理:")
        if browser.disable_proxy():
            time.sleep(2)
            final_ip = browser.test_ip()
            print(f"✅ 最终 IP: {final_ip}")
        
        print("\n✨ 测试完成！")
        print("\n💡 如果一切正常，你现在可以:")
        print("1. 使用 dataimpulse_browser.py 在你的项目中")
        print("2. 通过浏览器扩展的popup界面手动控制")
        print("3. 扩展ID已固定，下次启动会更快")
        
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        print("\n🔧 解决方案:")
        print("1. 确保 dataimpulse_proxy_extension/ 文件夹存在")
        print("2. 确保文件夹中有 manifest.json 和 background.js")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 检查Chrome浏览器是否正确安装")
        print("2. 检查DrissionPage是否正确安装: pip install DrissionPage")
        print("3. 检查用户名和密码是否正确")
        print("4. 检查网络连接是否正常")
        
    finally:
        try:
            browser.close()
        except:
            pass


if __name__ == "__main__":
    main()
