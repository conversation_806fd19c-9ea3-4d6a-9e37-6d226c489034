
<div>
  <section class="settings-group">
    <h3>{{'options_group_virtualProfile' | tr}}</h3>
    <p class="help-block">{{'options_virtualProfileTargetHelp' | tr}}</p>
    <div class="form-group">
      <label>{{'options_virtualProfileTarget' | tr}}</label> 
      <div omega-profile-select="options | profiles:profile" ng-model="profile.defaultProfileName" disp-name="dispNameFilter" style="display: inline-block;" options="options"></div>
    </div>
  </section>
  <section class="settings-group">
    <h3>{{'options_group_virtualProfileReplace' | tr}}</h3>
    <p omega-html="&quot;options_virtualProfileReplaceHelp&quot; | tr:[$profile(&quot;profileByName(profile.defaultProfileName)&quot;)]" class="help-block"></p>
    <div class="form-group">
      <button ng-click="replaceProfile(profile.defaultProfileName, profile.name)" class="btn btn-default"><span class="glyphicon glyphicon-search"></span> {{'options_virtualProfileReplace' | tr}}
      </button>
    </div>
  </section>
</div>