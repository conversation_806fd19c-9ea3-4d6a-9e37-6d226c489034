import time
import os
from DrissionPage import ChromiumPage, ChromiumOptions
import json
from string import Template
import threading

# 全局配置 - 一次设置，全局使用
PROXY_EXTENSION_PATH = os.path.join(os.getcwd(), 'shared_proxy_extension')
PROXY_EXTENSION_ID = None  # 第一次运行时自动获取并缓存


def create_shared_proxy_extension():
    """
    创建共享的代理扩展（所有浏览器实例共用）
    只需要创建一次，不需要每次都重新生成
    """
    global PROXY_EXTENSION_ID

    if os.path.exists(PROXY_EXTENSION_PATH):
        print("✅ 使用现有的代理扩展")
        return PROXY_EXTENSION_PATH

    print("🔧 创建共享代理扩展...")

    manifest_json = {
        "manifest_version": 3,
        "name": "Shared Proxy Extension",
        "version": "1.0.0",
        "permissions": [
            "proxy",
            "webRequest",
            "webRequestAuthProvider",
            "storage"
        ],
        "host_permissions": ["<all_urls>"],
        "externally_connectable": {
            "matches": ["<all_urls>"]
        },
        "background": {
            "service_worker": "background.js"
        }
    }

    # 支持动态配置的后台脚本
    background_js = """
// 默认代理配置（会被动态更新）
let proxyConfig = {
    host: 'default.proxy.com',
    port: 8080,
    username: 'default',
    password: 'default',
    scheme: 'http'
};

// 设置代理函数
function setProxy(config) {
    console.log('🔄 设置代理:', config);

    chrome.proxy.settings.set({
        value: {
            mode: "fixed_servers",
            rules: {
                singleProxy: {
                    scheme: config.scheme,
                    host: config.host,
                    port: parseInt(config.port)
                },
                bypassList: ["localhost", "127.0.0.1", "::1"]
            }
        },
        scope: "regular"
    }, function() {
        if (chrome.runtime.lastError) {
            console.error('❌ 代理设置失败:', chrome.runtime.lastError);
        } else {
            console.log('✅ 代理设置成功:', config.host + ':' + config.port);
        }
    });
}

// 处理代理认证
chrome.webRequest.onAuthRequired.addListener(
    (details) => {
        console.log('🔐 处理代理认证:', details.challenger.host);
        return {
            authCredentials: {
                username: proxyConfig.username,
                password: proxyConfig.password
            }
        };
    },
    {urls: ["<all_urls>"]},
    ["blocking"]
);

// 监听外部消息（来自网页）
chrome.runtime.onMessageExternal.addListener(
    (message, sender, sendResponse) => {
        const startTime = Date.now();
        console.log('📨 收到消息:', message.type, '来自:', sender.tab?.url || sender.url);

        try {
            if (message.type === 'updateProxy') {
                const oldConfig = {...proxyConfig};

                // 更新配置
                proxyConfig = {
                    host: message.host,
                    port: message.port,
                    username: message.username,
                    password: message.password,
                    scheme: message.scheme || 'http'
                };

                // 应用新配置
                setProxy(proxyConfig);

                const elapsed = Date.now() - startTime;
                console.log(`✅ 代理更新完成，耗时: ${elapsed}ms`);

                sendResponse({
                    success: true,
                    message: 'Proxy updated successfully',
                    elapsed: elapsed,
                    oldConfig: oldConfig,
                    newConfig: proxyConfig
                });

            } else if (message.type === 'getProxy') {
                sendResponse({
                    success: true,
                    config: proxyConfig,
                    extensionId: chrome.runtime.id
                });

            } else if (message.type === 'ping') {
                sendResponse({
                    success: true,
                    message: 'Extension is alive',
                    extensionId: chrome.runtime.id
                });

            } else {
                sendResponse({
                    success: false,
                    message: 'Unknown message type: ' + message.type
                });
            }
        } catch (error) {
            console.error('❌ 处理消息出错:', error);
            sendResponse({
                success: false,
                message: error.toString()
            });
        }

        return true; // 保持异步响应通道开放
    }
);

// 扩展启动时的初始化
chrome.runtime.onStartup.addListener(() => {
    console.log('🚀 代理扩展启动');
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('📦 代理扩展安装完成，ID:', chrome.runtime.id);
});

console.log('🎯 共享代理扩展已加载');
"""

    # 创建扩展文件
    os.makedirs(PROXY_EXTENSION_PATH, exist_ok=True)

    with open(os.path.join(PROXY_EXTENSION_PATH, "manifest.json"), "w") as f:
        json.dump(manifest_json, f, indent=2)

    with open(os.path.join(PROXY_EXTENSION_PATH, "background.js"), "w") as f:
        f.write(background_js)

    print(f"✅ 扩展创建完成: {PROXY_EXTENSION_PATH}")
    return PROXY_EXTENSION_PATH


def get_extension_id_once():
    """
    只获取一次扩展ID，全局缓存
    """
    global PROXY_EXTENSION_ID

    if PROXY_EXTENSION_ID:
        return PROXY_EXTENSION_ID

    print("🔍 获取扩展ID...")

    # 创建临时浏览器获取ID
    temp_co = ChromiumOptions().add_extension(path=PROXY_EXTENSION_PATH)
    temp_page = ChromiumPage(temp_co)

    try:
        time.sleep(2)  # 等待扩展加载

        # 通过ping消息获取ID
        result = temp_page.run_js("""
            new Promise((resolve, reject) => {
                // 尝试几个可能的扩展ID模式
                const possibleIds = [
                    chrome.runtime?.id,  // 如果在扩展内运行
                ];

                // 由于我们不知道确切ID，尝试向所有可能的扩展发送ping
                // 实际上，我们需要从chrome://extensions页面获取

                // 跳转到扩展页面获取ID
                window.location.href = 'chrome://extensions/';
                setTimeout(() => {
                    const extensions = document.querySelectorAll('extensions-item');
                    for (let ext of extensions) {
                        const nameEl = ext.shadowRoot?.querySelector('#name');
                        if (nameEl && nameEl.textContent.includes('Shared Proxy Extension')) {
                            const idEl = ext.shadowRoot?.querySelector('#extension-id');
                            if (idEl) {
                                resolve(idEl.textContent.replace('ID: ', ''));
                                return;
                            }
                        }
                    }
                    reject('Extension ID not found');
                }, 2000);
            })
        """)

        if result:
            PROXY_EXTENSION_ID = result
            print(f"✅ 获取到扩展ID: {PROXY_EXTENSION_ID}")
        else:
            # 备用方案：使用默认ID模式
            PROXY_EXTENSION_ID = "nkbihfbeogaeaoehlefnkodbefgpgknn"  # 示例ID
            print(f"⚠️ 使用备用ID: {PROXY_EXTENSION_ID}")

    except Exception as e:
        print(f"❌ 获取扩展ID失败: {e}")
        PROXY_EXTENSION_ID = "nkbihfbeogaeaoehlefnkodbefgpgknn"  # 备用ID

    finally:
        temp_page.quit()

    return PROXY_EXTENSION_ID


class FastProxyBrowser:
    """
    快速代理切换浏览器
    - 不需要每次获取扩展ID
    - 切换代理只需要0.5-1秒
    """

    def __init__(self, initial_proxy=None, browser_id=None):
        self.browser_id = browser_id or f"browser_{int(time.time())}"
        self.page = None
        self.current_proxy = initial_proxy
        self._setup_browser()

    def _setup_browser(self):
        """快速设置浏览器（使用共享扩展）"""
        print(f"🚀 启动浏览器 {self.browser_id}...")
        start_time = time.time()

        # 使用共享扩展
        co = ChromiumOptions().add_extension(path=PROXY_EXTENSION_PATH)
        co.add_argument('--disable-web-security')
        co.add_argument('--disable-dev-shm-usage')

        self.page = ChromiumPage(co)

        elapsed = time.time() - start_time
        print(f"✅ 浏览器 {self.browser_id} 启动完成，耗时: {elapsed:.2f}秒")

        # 如果有初始代理，设置它
        if self.current_proxy:
            time.sleep(1)  # 短暂等待扩展加载
            self.switch_proxy(**self.current_proxy)

    def switch_proxy(self, host, port, username, password, scheme='http'):
        """极速切换代理"""
        print(f"⚡ [{self.browser_id}] 切换代理: {host}:{port}")
        start_time = time.time()

        try:
            result = self.page.run_js(f"""
                new Promise((resolve, reject) => {{
                    const timeout = setTimeout(() => {{
                        reject('Timeout: Extension not responding');
                    }}, 3000);

                    chrome.runtime.sendMessage(
                        '{PROXY_EXTENSION_ID}',
                        {{
                            type: 'updateProxy',
                            host: '{host}',
                            port: {port},
                            username: '{username}',
                            password: '{password}',
                            scheme: '{scheme}'
                        }},
                        function(response) {{
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {{
                                reject('Chrome error: ' + chrome.runtime.lastError.message);
                            }} else {{
                                resolve(response);
                            }}
                        }}
                    );
                }})
            """)

            elapsed = time.time() - start_time

            if result and result.get('success'):
                self.current_proxy = {'host': host, 'port': port, 'username': username, 'password': password}
                print(f"✅ [{self.browser_id}] 代理切换成功，耗时: {elapsed:.2f}秒")
                return True, result
            else:
                print(f"❌ [{self.browser_id}] 代理切换失败，耗时: {elapsed:.2f}秒")
                return False, result

        except Exception as e:
            elapsed = time.time() - start_time
            print(f"💥 [{self.browser_id}] 代理切换异常，耗时: {elapsed:.2f}秒，错误: {e}")
            return False, str(e)

    def get_ip(self):
        """获取当前IP"""
        try:
            self.page.get('https://httpbin.org/ip', timeout=8)
            return self.page.json.get('origin', 'Unknown')
        except:
            return 'Failed to get IP'

    def get(self, url, **kwargs):
        """访问网页"""
        return self.page.get(url, **kwargs)

    def close(self):
        """关闭浏览器"""
        if self.page:
            print(f"🔒 关闭浏览器 {self.browser_id}")
            self.page.quit()


class ProxyBrowserPool:
    """
    代理浏览器池管理器
    - 快速创建多个浏览器实例
    - 支持动态代理切换
    """

    def __init__(self):
        self.browsers = {}
        self.setup_done = False

    def setup(self):
        """一次性设置（创建扩展、获取ID）"""
        if self.setup_done:
            return

        print("🔧 初始化代理浏览器池...")
        start_time = time.time()

        # 1. 创建共享扩展
        create_shared_proxy_extension()

        # 2. 获取扩展ID（只需要一次）
        get_extension_id_once()

        elapsed = time.time() - start_time
        print(f"✅ 浏览器池初始化完成，耗时: {elapsed:.2f}秒")
        self.setup_done = True

    def create_browser(self, browser_id, initial_proxy=None):
        """快速创建浏览器实例"""
        self.setup()  # 确保已初始化

        if browser_id in self.browsers:
            print(f"⚠️ 浏览器 {browser_id} 已存在")
            return self.browsers[browser_id]

        browser = FastProxyBrowser(initial_proxy, browser_id)
        self.browsers[browser_id] = browser
        return browser

    def get_browser(self, browser_id):
        """获取浏览器实例"""
        return self.browsers.get(browser_id)

    def switch_proxy_for_browser(self, browser_id, **proxy_config):
        """为指定浏览器切换代理"""
        browser = self.browsers.get(browser_id)
        if browser:
            return browser.switch_proxy(**proxy_config)
        return False, "Browser not found"

    def close_browser(self, browser_id):
        """关闭指定浏览器"""
        browser = self.browsers.pop(browser_id, None)
        if browser:
            browser.close()

    def close_all(self):
        """关闭所有浏览器"""
        for browser in self.browsers.values():
            browser.close()
        self.browsers.clear()


# 使用示例和性能测试
if __name__ == "__main__":
    # 性能测试：创建多个浏览器实例
    print("🚀 开始性能测试...")

    pool = ProxyBrowserPool()

    # 测试数据
    proxies = [
        {'host': 'proxy1.example.com', 'port': 8080, 'username': 'user1', 'password': 'pass1'},
        {'host': 'proxy2.example.com', 'port': 8081, 'username': 'user2', 'password': 'pass2'},
        {'host': 'proxy3.example.com', 'port': 8082, 'username': 'user3', 'password': 'pass3'},
    ]

    # 快速创建多个浏览器
    start_time = time.time()
    browsers = []

    for i, proxy in enumerate(proxies):
        browser_id = f"test_browser_{i + 1}"
        browser = pool.create_browser(browser_id, proxy)
        browsers.append(browser)

    creation_time = time.time() - start_time
    print(f"✅ 创建 {len(browsers)} 个浏览器耗时: {creation_time:.2f}秒")
    print(f"   平均每个浏览器: {creation_time / len(browsers):.2f}秒")

    # 测试快速代理切换
    print(f"\n⚡ 测试快速代理切换...")
    switch_times = []

    for i, browser in enumerate(browsers):
        # 切换到不同的代理
        new_proxy = proxies[(i + 1) % len(proxies)]

        start = time.time()
        success, result = browser.switch_proxy(**new_proxy)
        switch_time = time.time() - start

        switch_times.append(switch_time)
        print(f"  浏览器{i + 1} 切换耗时: {switch_time:.2f}秒 {'✅' if success else '❌'}")

    avg_switch_time = sum(switch_times) / len(switch_times)
    print(f"\n📊 平均切换时间: {avg_switch_time:.2f}秒")

    # 清理
    print(f"\n🧹 清理资源...")
    pool.close_all()
    print("✨ 测试完成")