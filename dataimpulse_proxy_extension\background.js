// DataImpulse代理扩展 - 后台服务工作者
console.log('🚀 DataImpulse代理扩展已启动');

// 预配置的DataImpulse代理服务器
const DATAIMPULSE_PROXIES = {
  proxy1: {
    name: 'DataImpulse Proxy 1',
    host: 'gw.dataimpulse.com',
    port: 19965,
    scheme: 'http'
  },
  proxy2: {
    name: 'DataImpulse Proxy 2', 
    host: 'gw.dataimpulse.com',
    port: 19966,
    scheme: 'http'
  }
};

// 当前代理配置
let currentProxy = {
  host: '',
  port: 0,
  username: '',
  password: '',
  scheme: 'http',
  enabled: false
};

// 设置代理函数
function setProxy(config) {
  console.log('🔄 设置代理:', config);
  
  if (!config.enabled) {
    // 禁用代理
    chrome.proxy.settings.set({
      value: { mode: "direct" },
      scope: "regular"
    }, function() {
      if (chrome.runtime.lastError) {
        console.error('❌ 禁用代理失败:', chrome.runtime.lastError);
      } else {
        console.log('✅ 代理已禁用');
      }
    });
    return;
  }

  chrome.proxy.settings.set({
    value: {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: config.scheme,
          host: config.host,
          port: parseInt(config.port)
        },
        bypassList: ["localhost", "127.0.0.1", "::1"]
      }
    },
    scope: "regular"
  }, function() {
    if (chrome.runtime.lastError) {
      console.error('❌ 代理设置失败:', chrome.runtime.lastError);
    } else {
      console.log('✅ 代理设置成功:', config.host + ':' + config.port);
    }
  });
}

// 处理代理认证
chrome.webRequest.onAuthRequired.addListener(
  (details) => {
    console.log('🔐 处理代理认证:', details.challenger.host);
    if (currentProxy.username && currentProxy.password) {
      return {
        authCredentials: {
          username: currentProxy.username,
          password: currentProxy.password
        }
      };
    }
    return {};
  },
  {urls: ["<all_urls>"]},
  ["blocking"]
);

// IP测试函数
async function testCurrentIP() {
  try {
    console.log('🌐 开始IP测试...');
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    console.log('✅ 当前IP:', data.ip);
    return { success: true, ip: data.ip };
  } catch (error) {
    console.error('❌ IP测试失败:', error);
    return { success: false, error: error.message };
  }
}

// 快速切换到DataImpulse代理
function switchToDataImpulseProxy(proxyKey, username, password) {
  const proxy = DATAIMPULSE_PROXIES[proxyKey];
  if (!proxy) {
    return { success: false, message: 'Unknown proxy key: ' + proxyKey };
  }

  currentProxy = {
    host: proxy.host,
    port: proxy.port,
    username: username,
    password: password,
    scheme: proxy.scheme,
    enabled: true
  };

  setProxy(currentProxy);
  return { 
    success: true, 
    message: `已切换到 ${proxy.name}`,
    proxy: currentProxy
  };
}

// 监听外部消息
chrome.runtime.onMessageExternal.addListener(
  async (message, sender, sendResponse) => {
    const startTime = Date.now();
    console.log('📨 收到消息:', message.type, '来自:', sender.tab?.url || sender.url);

    try {
      if (message.type === 'updateProxy') {
        currentProxy = {
          host: message.host,
          port: message.port,
          username: message.username,
          password: message.password,
          scheme: message.scheme || 'http',
          enabled: true
        };

        setProxy(currentProxy);
        const elapsed = Date.now() - startTime;
        
        sendResponse({
          success: true,
          message: 'Proxy updated successfully',
          elapsed: elapsed,
          proxy: currentProxy
        });

      } else if (message.type === 'switchDataImpulse') {
        const result = switchToDataImpulseProxy(
          message.proxyKey, 
          message.username, 
          message.password
        );
        
        const elapsed = Date.now() - startTime;
        sendResponse({
          ...result,
          elapsed: elapsed
        });

      } else if (message.type === 'testIP') {
        const result = await testCurrentIP();
        const elapsed = Date.now() - startTime;
        
        sendResponse({
          ...result,
          elapsed: elapsed
        });

      } else if (message.type === 'getProxy') {
        sendResponse({
          success: true,
          proxy: currentProxy,
          extensionId: chrome.runtime.id
        });

      } else if (message.type === 'disableProxy') {
        currentProxy.enabled = false;
        setProxy(currentProxy);
        
        sendResponse({
          success: true,
          message: 'Proxy disabled',
          elapsed: Date.now() - startTime
        });

      } else if (message.type === 'ping') {
        sendResponse({
          success: true,
          message: 'DataImpulse Extension is alive',
          extensionId: chrome.runtime.id,
          availableProxies: Object.keys(DATAIMPULSE_PROXIES)
        });

      } else {
        sendResponse({
          success: false,
          message: 'Unknown message type: ' + message.type
        });
      }
    } catch (error) {
      console.error('❌ 处理消息出错:', error);
      sendResponse({
        success: false,
        message: error.toString()
      });
    }

    return true; // 保持异步响应通道开放
  }
);

// 扩展启动时的初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('🚀 DataImpulse代理扩展启动');
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('📦 DataImpulse代理扩展安装完成，ID:', chrome.runtime.id);
  console.log('🎯 可用代理:', Object.keys(DATAIMPULSE_PROXIES));
});

console.log('🎯 DataImpulse代理扩展已加载，支持快速IP测试和代理切换');
