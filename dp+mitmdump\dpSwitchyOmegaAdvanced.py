#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SwitchyOmega代理管理方案
基于dpMitmdumpTest.py配置，支持动态切换代理IP
"""

import os
import time
from DrissionPage import ChromiumPage, ChromiumOptions


class SwitchyOmegaAdvanced:
    """SwitchyOmega高级代理管理器"""
    
    def __init__(self):
        self.page = None
        
        # 获取扩展路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        
        self.switchyomega_path = os.path.join(script_dir, "chromium-release")
        self.ua_patch_path = os.path.join(project_root, "cloudflare_ua_patch")
        
        # 正确的SwitchyOmega扩展ID
        self.extension_id = "fjplonlgeikcikbiffklcbdikcaienob"
        
        # 代理配置列表（支持多个代理）
        self.proxy_configs = [
            {
                "name": "CloudflareBypass",
                "host": "gw.dataimpulse.com",
                "port": 19965,
                "username": "0465846b31e1f583b17c__cr.us",
                "password": "16af34bf75f0573a",
                "scheme": "https"
            },
            # 可以添加更多代理配置
        ]
        
        self.current_proxy_index = 0
    
    def create_browser(self, headless=False, user_agent=None):
        """创建浏览器实例（完全基于dpMitmdumpTest.py配置）"""
        print("🚀 创建浏览器实例...")

        options = ChromiumOptions()

        # 设置浏览器路径（与dpMitmdumpTest.py完全一致）
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")

        # 启动参数（与dpMitmdumpTest.py完全一致）
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768'
        ]

        # 无头模式
        if headless:
            args.append('--headless')

        # 自定义User-Agent
        if user_agent:
            args.append(f'--user-agent={user_agent}')

        # 应用所有参数（与dpMitmdumpTest.py一致）
        [options.set_argument(arg) for arg in args]

        # 添加cloudflare_ua_patch扩展（与dpMitmdumpTest.py加载顺序一致）
        if os.path.exists(self.ua_patch_path):
            print(f"✅ 加载cloudflare_ua_patch扩展: {self.ua_patch_path}")
            options.add_extension(self.ua_patch_path)
        else:
            print("⚠️ cloudflare_ua_patch扩展未找到，可能影响Cloudflare绕过效果")

        # 添加SwitchyOmega扩展
        if os.path.exists(self.switchyomega_path):
            print(f"✅ 加载SwitchyOmega扩展: {self.switchyomega_path}")
            options.add_extension(self.switchyomega_path)
        else:
            print("❌ SwitchyOmega扩展未找到")
            return False

        # 自动分配端口（与dpMitmdumpTest.py一致）
        options.auto_port()

        try:
            self.page = ChromiumPage(options)
            print("✅ 浏览器启动成功，扩展已加载")

            # 快速验证（参考dpMitmdumpTest.py）
            print("⏳ 验证扩展加载...")
            time.sleep(2)

            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            # 尝试关闭可能存在的浏览器进程
            try:
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], capture_output=True)
                subprocess.run(['taskkill', '/f', '/im', 'chromium.exe'], capture_output=True)
                print("🔄 已清理浏览器进程，请重试")
            except:
                pass
            return False
    
    def configure_proxy(self, proxy_config):
        """配置指定的代理（改进版）"""
        try:
            print(f"\n🔧 配置代理: {proxy_config['name']}")
            
            # 访问代理配置页面
            options_url = f"chrome-extension://{self.extension_id}/options.html#!/profile/proxy"
            print(f"📖 访问配置页面: {options_url}")
            
            self.page.get(options_url)
            time.sleep(2)
            
            # 验证页面加载
            if "SwitchyOmega" not in self.page.title and "Omega" not in self.page.title:
                print("❌ SwitchyOmega选项页面加载失败")
                return False
            
            print("✅ SwitchyOmega选项页面加载成功")
            
            # 1. 选择HTTPS协议（重要！）
            print("🔧 选择HTTPS协议...")
            try:
                # 尝试多种协议选择方式
                protocol_selected = False
                
                # 方法1: 通过select元素
                protocol_select = self.page.ele('x://select[@ng-model="scheme"]', timeout=3)
                if protocol_select:
                    protocol_select.select.by_value('https')
                    protocol_selected = True
                    print("✅ 通过select选择HTTPS协议")
                
                # 方法2: 直接点击HTTPS选项
                if not protocol_selected:
                    https_option = self.page.ele('x://option[@value="https"]', timeout=3)
                    if https_option:
                        https_option.click()
                        protocol_selected = True
                        print("✅ 通过点击选择HTTPS协议")
                
                # 方法3: 查找协议按钮
                if not protocol_selected:
                    https_button = self.page.ele('text:HTTPS', timeout=3)
                    if https_button:
                        https_button.click()
                        protocol_selected = True
                        print("✅ 通过按钮选择HTTPS协议")
                
                if not protocol_selected:
                    print("⚠️ 未找到协议选择器，使用默认协议")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️ 协议选择失败，使用默认: {e}")
            
            # 2. 配置服务器地址
            print("🔧 配置代理服务器...")
            host_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].host"]', timeout=5)
            if host_input:
                host_input.clear()
                host_input.input(proxy_config['host'])
                print(f"✅ 设置服务器: {proxy_config['host']}")
            else:
                print("❌ 未找到服务器地址输入框")
                return False
            
            # 3. 配置端口
            print("🔧 配置代理端口...")
            port_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].port"]', timeout=5)
            if port_input:
                port_input.clear()
                port_input.input(str(proxy_config['port']))
                print(f"✅ 设置端口: {proxy_config['port']}")
            else:
                print("❌ 未找到端口输入框")
                return False
            
            # 4. 配置认证
            print("🔧 配置代理认证...")
            auth_button = self.page.ele('x://button[@ng-click="editProxyAuth(scheme)"]', timeout=5)
            if auth_button:
                auth_button.click(by_js=True)
                time.sleep(1)
                
                # 输入用户名
                username_input = self.page.ele('x://input[@ng-model="model"]', timeout=5)
                if username_input:
                    username_input.clear()
                    username_input.click(by_js=True)
                    username_input.input(proxy_config['username'])
                    print(f"✅ 设置用户名")
                
                # 输入密码
                password_input = self.page.ele('x://input[@ng-model="auth.password"]', timeout=5)
                if password_input:
                    password_input.clear()
                    password_input.click(by_js=True)
                    password_input.input(proxy_config['password'])
                    print("✅ 设置密码")
                
                # 保存认证
                save_auth_button = self.page.ele('x://button[@ng-disabled="!authForm.$valid"]', timeout=5)
                if save_auth_button:
                    save_auth_button.click(by_js=True)
                    time.sleep(1)
                    print("✅ 认证配置已保存")
            
            # 5. 应用配置
            print("🔧 应用配置...")
            apply_button = self.page.ele('x://a[@ng-click="applyOptions()"]', timeout=5)
            if apply_button:
                apply_button.click(by_js=True)
                time.sleep(2)
                print("✅ 配置已应用")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置代理失败: {e}")
            return False
    
    def activate_proxy_mode(self):
        """激活代理模式（修复重复访问问题）"""
        try:
            print("\n🔧 激活代理模式...")

            # 不重复访问页面，直接在当前配置页面操作
            # 首先应用配置更改
            apply_button = self.page.ele('x://button[contains(@class, "btn-primary") and contains(text(), "Apply")]', timeout=5)
            if apply_button:
                apply_button.click(by_js=True)
                time.sleep(2)
                print("✅ 配置已应用")

            # 然后激活代理模式
            # 查找代理模式切换按钮
            dropdown_button = self.page.ele('x://button[@class="btn btn-default dropdown-toggle"]', timeout=5)
            if dropdown_button:
                dropdown_button.click(by_js=True)
                time.sleep(1)

                # 优先选择CloudflareBypass配置
                cloudflare_option = self.page.ele('x://a[contains(text(), "CloudflareBypass")]', timeout=3)
                if cloudflare_option:
                    cloudflare_option.click(by_js=True)
                    time.sleep(1)
                    print("✅ 已激活CloudflareBypass配置")
                    return True

                # 备选：选择proxy模式
                proxy_option = self.page.ele('text:proxy', timeout=3)
                if proxy_option:
                    proxy_option.click()
                    print("✅ 已激活proxy模式")
                    return True

            # 备用方法：通过JavaScript强制设置
            print("🔄 尝试JavaScript强制激活...")
            js_code = """
            // 强制设置启动配置文件
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                chrome.storage.local.set({
                    '-startupProfileName': 'CloudflareBypass'
                }, function() {
                    console.log('代理配置已设置');
                });
                return 'storage_success';
            }
            return 'failed';
            """

            result = self.page.run_js(js_code)
            if result == 'storage_success':
                print("✅ JavaScript强制激活成功")
                return True

            print("⚠️ 代理模式激活失败")
            return False

        except Exception as e:
            print(f"❌ 激活代理模式失败: {e}")
            return False
    
    def switch_proxy(self, proxy_index=None):
        """切换到指定代理"""
        if proxy_index is None:
            proxy_index = (self.current_proxy_index + 1) % len(self.proxy_configs)
        
        if proxy_index >= len(self.proxy_configs):
            print("❌ 代理索引超出范围")
            return False
        
        proxy_config = self.proxy_configs[proxy_index]
        
        if self.configure_proxy(proxy_config):
            if self.activate_proxy_mode():
                self.current_proxy_index = proxy_index
                print(f"🎉 成功切换到代理: {proxy_config['name']}")
                return True
        
        return False
    
    def test_proxy_status(self):
        """测试代理状态（增强验证）"""
        if not self.page:
            print("❌ 浏览器未初始化")
            return False

        try:
            print("\n🔍 验证代理状态...")

            # 首先检查SwitchyOmega状态
            print("📊 检查SwitchyOmega状态...")
            try:
                # 访问SwitchyOmega popup来检查当前状态
                popup_url = f"chrome-extension://{self.extension_id}/popup.html"
                self.page.get(popup_url)
                time.sleep(2)

                # 检查当前激活的配置
                active_profile = self.page.ele('x://span[@class="profile-name"]', timeout=3)
                if active_profile:
                    profile_name = active_profile.text
                    print(f"📋 当前激活配置: {profile_name}")

                    if "CloudflareBypass" not in profile_name and "proxy" not in profile_name.lower():
                        print("⚠️ 代理配置未激活，尝试手动激活...")
                        # 尝试点击CloudflareBypass配置
                        cloudflare_btn = self.page.ele('x://button[contains(text(), "CloudflareBypass")]', timeout=3)
                        if cloudflare_btn:
                            cloudflare_btn.click()
                            time.sleep(2)
                            print("✅ 手动激活CloudflareBypass配置")
                        else:
                            print("❌ 未找到CloudflareBypass配置按钮")
                else:
                    print("⚠️ 无法检测当前配置状态")
            except Exception as e:
                print(f"⚠️ SwitchyOmega状态检查失败: {e}")

            # 测试IP地址
            print("🌐 检查当前IP地址...")
            self.page.get("https://api.ipify.org/")
            time.sleep(3)

            ip_content = self.page.html
            import re
            ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
            current_ip = ip_match.group() if ip_match else "未知"

            print(f"🌐 当前IP: {current_ip}")

            # 检查是否是代理IP
            expected_proxy_ip = "**************"  # 之前成功的代理IP
            if current_ip == expected_proxy_ip:
                print("✅ 代理IP验证成功！")
            elif current_ip.startswith("103.151."):
                print("⚠️ 检测到非代理IP，代理可能未生效")
                return False
            else:
                print(f"🔄 检测到新的代理IP: {current_ip}")

            # 测试代理连接质量
            print("🔗 测试代理连接质量...")
            self.page.get("https://httpbin.org/ip")
            time.sleep(2)

            if "origin" in self.page.html:
                print("✅ 代理连接质量良好")

            # 测试X.com访问
            print("🐦 测试X.com访问...")
            self.page.get("https://x.com/account/access")
            time.sleep(3)

            if "x.com" in self.page.url or "twitter.com" in self.page.url:
                print("✅ X.com访问成功")
                return True
            else:
                print("⚠️ X.com访问可能有问题")
                return False

        except Exception as e:
            print(f"❌ 代理测试失败: {e}")
            return False


    
    def run_config(self):
        """运行SwitchyOmega代理配置"""
        print("\n🎮 SwitchyOmega代理管理")
        print("🎯 功能:")
        print("  🤖 自动化配置")
        print("  🔄 动态切换代理IP")
        print("  🔐 HTTPS代理认证")
        print("  ⚡ 自动激活代理模式")
        print()

        # 创建浏览器
        if not self.create_browser():
            return

        # 等待扩展加载
        print("⏳ 等待扩展加载...")
        time.sleep(3)

        # 配置并激活代理
        if self.switch_proxy(0):
            # 验证代理状态
            if self.test_proxy_status():
                print("\n🎉 SwitchyOmega配置成功！")
                print("✨ 功能已启用:")
                print("  ✅ 代理配置和激活")
                print("  ✅ Cloudflare站点访问")
                print("  ✅ 动态IP切换支持")
            else:
                print("\n⚠️ 代理验证失败")
        else:
            print("\n❌ 代理配置失败")

        print("\n🎯 配置完成！浏览器保持打开")
        print("💡 您可以继续使用浏览器进行其他操作")
        print("🔚 按Enter关闭...")

        input("⏳ 按Enter关闭浏览器...")
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                print("✅ 浏览器已关闭")
            except:
                pass


def main():
    """主函数"""
    try:
        manager = SwitchyOmegaAdvanced()
        manager.run_config()

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        if 'manager' in locals():
            manager.close()


if __name__ == "__main__":
    main()
