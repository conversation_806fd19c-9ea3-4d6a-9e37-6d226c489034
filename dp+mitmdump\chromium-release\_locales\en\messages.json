{"appNameShort": {"message": "ZeroOmega"}, "manifest_app_name": {"message": "ZeroOmega"}, "manifest_app_description": {"message": "Manage and switch between multiple proxies quickly & easily."}, "manifest_icon_default_title": {"message": "Loading…"}, "upgrade_profile_auto": {"message": "Auto Switch"}, "profile_direct": {"message": "[Direct]"}, "profile_direct_badge_text": {"message": "Direct"}, "profile_system": {"message": "[System Proxy]"}, "profile_system_badge_text": {"message": "System"}, "condition_HostWildcardCondition": {"message": "Host wildcard"}, "condition_help_HostWildcardCondition": {"message": "Matches hosts (domain names) by wildcard.<br><b>The asterisk <code>*</code></b> matches zero or more characters.<br><b>The question mark <code>?</code></b> matches exactly one character.<br><br>Note that rules beginning with <code>*.</code> are specially treated only in Host wildcard conditions.<br>Example: <code>*.example.com</code> will match www.example.com <b>AND example.com as well.</b><br>To match subdomains <b>only</b>, use <b>two</b> asterisks like <code>**.example.com</code>."}, "condition_HostRegexCondition": {"message": "Host regex"}, "condition_help_HostRegexCondition": {"message": "Like Host wildcard condition, but matches hosts (domain names) by <a href='https://www.google.com/search?q=regular%20expression'>regular expression</a>.<br>Regular expressions can be hard to construct (and read).<br>It is recommended to use wildcards for most cases and only use regex for conditions that cannot be achieved by any other condition type."}, "condition_HostLevelsCondition": {"message": "Host levels"}, "condition_help_HostLevelsCondition": {"message": "Matches the request if and only if the host level in within the given range.<br>Host level is defined as the <b>number of dot-separated segments</b> of the host (domain name).<br>Example: <code>www.example.com</code> is with a host level of 3, while <code>internal</code> is of host level 1."}, "condition_IpCondition": {"message": "IP Literals"}, "condition_help_IpCondition": {"message": "Matches the request if and only if the host is a <b>literal</b> IP address and in the subnet as specified by <a href='https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing#CIDR_notation'>CIDR notation</a>.<br>For example, given the rule <code>127.0.0.1/16</code>, it matches all IP addresses like <code>127.0.*.*</code>.<br>So <code>127.0.0.1</code> matches while <code>*********</code> does not. Host names like <code>localhost</code> will never match because they are <b>not</b> IP literals."}, "condition_UrlWildcardCondition": {"message": "URL wildcard"}, "condition_help_UrlWildcardCondition": {"message": "Matches URLs of the request by wildcard.<br>See the Host wildcard section above for a quick wildcard reference.<br>Note that URL wildcards are not specially treated (no subdomain magic as in Host wildcard).<br>So <code>*://*.example.com/*</code> matches http://www.example.com/ but <b>does not</b> match http://example.com/."}, "condition_UrlRegexCondition": {"message": "URL regex"}, "condition_help_UrlRegexCondition": {"message": "Matches URL by extremely powerful <a href='https://www.google.com/search?q=regular%20expression'>regular expression</a>.<br>However, regular expressions can be hard to construct (and read).<br>It is recommended to use wildcards for most cases and only use regex for conditions that cannot be achieved by any other condition type."}, "condition_KeywordCondition": {"message": "Keyword"}, "condition_help_KeywordCondition": {"message": "A keyword condition matches if the URL protocol is HTTP, and the pattern is an exact sub-string of the URL.<br>It behaves like the URL wildcard pattern <code>http://*<b>pattern</b>*</code>, where <b>pattern</b> is the keyword pattern.<br>Keyword conditions are useful if you want to bypass a firewall blocking some keywords in the URL, by requesting such URLs through a proxy."}, "condition_FalseCondition": {"message": "(Disabled)"}, "condition_details_FalseCondition": {"message": "(Condition ignored when matching)"}, "condition_help_FalseCondition": {"message": "You can disable a condition by setting its type to <code>(Disabled)</code>. A Disabled condition act as if it does not exist.<br>This feature can be used to disable conditions temporarily.<br>Disabled conditions still hold the previous information (like patterns) and can be re-enabled by setting the condition type back to the previous type."}, "condition_TimeCondition": {"message": "Current Time"}, "condition_help_TimeCondition": {"message": "Matches if the current local time is in the range defined by <b>starting hour</b> and <b>ending hour</b>, both inclusive.<br>Local time, starting hour and ending hour are all calculated in <b>24-hour</b> format (from <b>0 to 23</b>). <br>The calculation happens roughly at the moment when the request is sent."}, "condition_WeekdayCondition": {"message": "Day of the Week"}, "condition_help_WeekdayCondition": {"message": "Matches if the <b>current day of week</b> is selected in condition details. Day is calculated according to local timezone.<br>The request and its URL don't matter to this condition. The result is solely based on the day of the week when the request is sent."}, "condition_alert_fullUrlLimitation": {"message": "Full URL matching is no longer possible for <code>https://</code> URLs as of Chrome 52. <a href='https://github.com/FelisCatus/SwitchyOmega/wiki/Chromium-Full-URL-Limitation'>Learn more...</a>"}, "condition_alert_fullUrlLimitationLink": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/Chromium-Full-URL-Limitation"}, "condition_group_default": {"message": ""}, "condition_group_host": {"message": "Host"}, "condition_group_url": {"message": "Url"}, "condition_group_special": {"message": "Special"}, "ruleListFormat_Switchy": {"message": "Switchy"}, "ruleListFormat_AutoProxy": {"message": "AutoProxy"}, "ruleList_usageUrl": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/RuleListUsage"}, "ruleList_error_resultNotEnabled": {"message": "Missing '@with result' directive!"}, "ruleList_error_unknownProfile": {"message": "Unknown profile: $PROFILE$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "ruleList_error_missingResultProfile": {"message": "Missing result profile name at Line $LNO$: $SOURCE$", "placeholders": {"_unused_0": {"content": "$0"}, "LNO": {"content": "$1"}, "SOURCE": {"content": "$2"}}}, "ruleList_error_invalidRule": {"message": "Invalid rule at Line $LNO$: $SOURCE$", "placeholders": {"_unused_0": {"content": "$0"}, "LNO": {"content": "$1"}, "SOURCE": {"content": "$2"}}}, "ruleList_error_noDefaultRule": {"message": "Missing default rule with catch-all '*' condition!"}, "dialog_close": {"message": "Close"}, "dialog_save": {"message": "Save changes"}, "dialog_ok": {"message": "OK"}, "dialog_cancel": {"message": "Cancel"}, "inputClear_clear": {"message": "Clear"}, "inputClear_restore": {"message": "Rest<PERSON>"}, "options_title": {"message": "ZeroOmega Options"}, "options_experimental_badge": {"message": "α"}, "options_navHeader_setting": {"message": "Settings"}, "options_navHeader_profiles": {"message": "Profiles"}, "options_navHeader_actions": {"message": "Actions"}, "options_tab_ui": {"message": "Interface"}, "options_tab_general": {"message": "General"}, "options_tab_importExport": {"message": "Import/Export"}, "options_newProfile": {"message": "New profile…"}, "options_apply": {"message": "Apply changes"}, "options_discard": {"message": "Discard changes"}, "options_reset": {"message": "Reset options"}, "options_group_miscOptions": {"message": "Misc Options"}, "options_confirmDeletion": {"message": "Confirm on condition deletion."}, "options_refreshOnProfileChange": {"message": "Refresh current tab on profile change."}, "options_showInspectMenu": {"message": "Allow inspecting proxy used for page elements via context menu."}, "options_addConditionsToBottom": {"message": "Put new conditions added using the popup to the bottom of the list."}, "options_showResultProfileOnActionBadgeText": {"message": "Show the result profile's name on the action badge text."}, "options_group_keyboardShortcut": {"message": "Keyboard Shortcut"}, "options_menuShortcutHelp": {"message": "Pressing the shortcut will open the switch popup menu. (Defaults to Alt+Shift+O)."}, "options_menuShortcutMore": {"message": "The items in the popup menu can also be accessed using the keyboard. Press ? (or /) in the menu to learn more."}, "options_menuShortcutConfigure": {"message": "Configure shortcut"}, "options_group_switchOptions": {"message": "Switch Options"}, "options_startupProfile": {"message": "Startup Profile"}, "options_startupProfile_none": {"message": "(Current profile)"}, "options_showConditionTypesAdvanced": {"message": "Show advanced condition types"}, "options_showConditionTypesAdvancedHelp": {"message": "Unlocks new types of advanced but complicated switch conditions. For most scenarios, the basic condition types should be enough, so this option is not recommended."}, "options_quickSwitch": {"message": "Quick Switch"}, "options_cycledProfiles": {"message": "Cycled Profiles"}, "options_cycledProfilesHelp": {"message": "When you click on the icon (or use the shortcut above), the following profiles will be applied in their order."}, "options_cycledProfilesTooFew": {"message": "You need to select at least 2 profiles to enable this function! You can drag them from the box below."}, "options_notCycledProfiles": {"message": "Not Cycled Profiles"}, "options_group_proxyChanges": {"message": "Proxy Changes"}, "options_revertProxyChanges": {"message": "Revert proxy changes done by other apps."}, "options_group_conflicts": {"message": "Conflicts"}, "options_conflicts_introduction": {"message": "Sometimes, other apps will also try to control the proxy settings, resulting in conflicts. Note that ad blockers and other extensions may also use proxy settings under the hood. Such conflicts cannot be avoided due to how the browser works."}, "options_conflicts_lowerPriority": {"message": "A red badge like this on the ZeroOmega icon indicates that another app has higher priority so ZeroOmega cannot control the settings. Please try to uninstall ZeroOmega and reinstall, which should raise ZeroOmega's priority. If you still see conflicts after reinstallation, please consider removing the other app causing the conflict."}, "options_conflicts_higherPriority": {"message": "If ZeroOmega has higher priority, you can give the control back to other apps or system settings by selecting $SYSTEMPROFILE$ in the popup menu.", "placeholders": {"_unused_0": {"content": "$0"}, "SYSTEMPROFILE": {"content": "$1"}}}, "options_showExternalProfile": {"message": "Show popup menu item to import proxy settings from other apps."}, "options_showExternalProfileHelp": {"message": "When $SYSTEMPROFILE$ is selected, you can import the effective proxy settings from other apps by selecting $EXTERNALPROFILE$ on the popup menu. The settings will be imported as a profile using the name you provide. Please note that the imported profile is a snapshot and will not reflect any changes from the source app thereafter.", "placeholders": {"_unused_0": {"content": "$0"}, "SYSTEMPROFILE": {"content": "$1"}, "EXTERNALPROFILE": {"content": "$2"}}}, "options_group_networkRequests": {"message": "Network Requests"}, "options_monitorWebRequests": {"message": "Show count of failed web requests for resources in the current tab."}, "options_monitorWebRequestsHelp": {"message": "A yellow badge will be displayed on the icon if some resources fail to load,<br>and you can set the profile for such resources conveniently via the popup menu."}, "options_downloadOptions": {"message": "Download Options"}, "options_downloadOptionsHelp": {"message": "Configure the update frequency of online rule lists and PAC scripts."}, "options_downloadInterval": {"message": "Download Interval"}, "options_downloadInterval_15": {"message": "15 Minutes"}, "options_downloadInterval_60": {"message": "1 Hour"}, "options_downloadInterval_180": {"message": "3 Hours"}, "options_downloadInterval_360": {"message": "6 Hours"}, "options_downloadInterval_720": {"message": "12 Hours"}, "options_downloadInterval_1440": {"message": "Every day"}, "options_downloadInterval_never": {"message": "Never"}, "options_group_importExportProfile": {"message": "Profile"}, "options_exportPacFile": {"message": "Export as PAC File"}, "options_exportPacFileHelp": {"message": "Export the current profile as a PAC file, so you can use it in other browsers."}, "options_exportProfileHelp": {"message": "To export a profile, use the top-right action bar on the profile page."}, "options_exportLegacyRuleList": {"message": "Export rule lists using Proxy Switchy!/SwitchyPlus/SwitchySharp compatible format when possible."}, "options_exportLegacyRuleListHelp": {"message": "Enable this option only if you publish rule lists for users of those projects.<br>Please consider advising your audience to upgrade to ZeroOmega for the improvements."}, "options_group_importExportSettings": {"message": "Settings"}, "options_makeBackup": {"message": "Make backup"}, "options_makeBackupHelp": {"message": "Make a full backup of your options (including profiles and all other options)."}, "options_restoreLocal": {"message": "Restore from file"}, "options_restoreLocalHelp": {"message": "Restore your ZeroOmega options from a local file."}, "options_restoreOnline": {"message": "Restore from online"}, "options_restoreOnlinePlaceholder": {"message": "Options file URL (e.g. 'http://example.com/switchy.bak')"}, "options_restoreOnlineSubmit": {"message": "Rest<PERSON>"}, "options_group_syncing": {"message": "Syncing (Experimental)"}, "options_syncEnable": {"message": "Enable Syncing"}, "options_useBuiltInSyncEnhance": {"message": "Enhance your Gist sync experience with built-in browser sync"}, "options_useBuiltInSyncEnhanceTip": {"message": "<li>After the user logs in to the browser account, the extension will be automatically downloaded and all synchronized settings will be restored</li><li>When the configuration items are changed, they will be synchronized to other browsers immediately. (When this feature is not turned on, the application will only check whether the configuration has changed every 5 minutes)</li>"}, "options_syncEnableForce": {"message": "Download from Syncing"}, "options_syncDisable": {"message": "Disable syncing"}, "options_syncReset": {"message": "Clear remote copy"}, "options_syncPristineHelp": {"message": "You can now automatically synchronize your settings and profiles across all your desktop devices running Chrome browser."}, "options_syncSyncAlert": {"message": "Your options are automatically synchronized with your other devices."}, "options_syncSyncHelp": {"message": "Please note that you must sign in to Chrome on each of your devices (including this one) for the syncing to actually work. <br> You may check this section on other devices to ensure that it is working."}, "options_syncConflictAlert": {"message": "You have uploaded a copy of your options on another device via syncing."}, "options_syncConflictHelp": {"message": "You may download the remote copy to your device if you like. <br>However, doing so would <b>overwrite your existing settings and profiles</b> on this device."}, "options_syncUnsupportedHelp": {"message": "Options syncing is not supported on your platform or browser. For now, only Chrome browser on desktop is supported."}, "options_builtin": {"message": "Builtin"}, "options_theme": {"message": "Theme"}, "options_builtinProfile": {"message": "Builtin Profiles"}, "options_profileTabPrefix": {"message": "Profile :: "}, "options_renameProfile": {"message": "<PERSON><PERSON>"}, "options_deleteProfile": {"message": "Delete"}, "options_profileExportRuleList": {"message": "Publish rule list"}, "options_profileExportRuleListHelp": {"message": "Export Switch Rules as text format for publishing."}, "options_profileExportPac": {"message": "Export PAC"}, "options_profileUnsupported": {"message": "Unsupported profile type $TYPE$!", "placeholders": {"_unused_0": {"content": "$0"}, "TYPE": {"content": "$1"}}}, "options_profileUnsupportedHelp": {"message": "The options could be broken, or from a newer version of this program."}, "options_profileEditSource": {"message": "Edit source code"}, "options_profileEditSourceHelp": {"message": "Show help about the source code format"}, "options_profileEditSourceHelpUrl": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/SwitchyOmega-conditions-format#result-profile"}, "options_group_proxyServers": {"message": "Proxy servers"}, "options_proxy_scheme": {"message": "Scheme"}, "options_proxy_protocol": {"message": "Protocol"}, "options_proxy_server": {"message": "Server"}, "options_proxy_port": {"message": "Port"}, "options_proxy_auth": {"message": "Authentication"}, "options_proxy_authNotSupported": {"message": "Your browser DOES NOT support $PROTOCOLDISP$ proxy authentication! Please do not report this issue to ZeroOmega. Contact the support for your browser instead.", "placeholders": {"_unused_0": {"content": "$0"}, "PROTOCOLDISP": {"content": "$1"}}}, "options_proxy_authAllWarningPac": {"message": "Warning: The username/password may be sent to unexpected servers returned by the PAC script."}, "options_proxy_authAllWarningPacUrl": {"message": "Please make sure that you trust the script provided via the URL above before entering sensitive credentials."}, "options_proxy_authAllWarningPacScript": {"message": "Please make sure that you trust the script below before providing sensitive credentials."}, "options_proxy_authReferencedWarning": {"message": "Additionally, using this profile in other profiles (e.g. Switch Profile) may cause the username/password to be sent to proxy servers configured in other profiles."}, "options_scheme_default": {"message": "(default)"}, "options_protocol_direct": {"message": "DIRECT"}, "options_protocol_useDefault": {"message": "(use default)"}, "options_proxy_single": {"message": "Use the proxy above for all protocols."}, "options_proxy_expand": {"message": "Show Advanced"}, "options_group_bypassList": {"message": "Bypass List"}, "options_bypassListHelp": {"message": "Servers for which you do not want to use any proxy: (One server on each line.)"}, "options_bypassListHelpLinkText": {"message": "(Wildcards and more available…)"}, "options_group_pacUrl": {"message": "PAC URL"}, "options_pacUrlHelp": {"message": "The PAC script will be updated from this URL. If it is left blank, the following script will be used directly instead."}, "options_pacUrlFile": {"message": "PAC profiles with file: URLs can only be applied directly. They cannot be used as result profiles because local files cannot be accessed due to browser limitation."}, "options_pacUrlFileDisabled": {"message": "Therefore, you cannot use local PAC file for this profile. You can create a new PAC profile for that if you really want that."}, "options_group_pacScript": {"message": "PAC Script"}, "options_pacScriptLastUpdate": {"message": "PAC script downloaded at $TIME$:", "placeholders": {"_unused_0": {"content": "$0"}, "TIME": {"content": "$1"}}}, "options_pacScriptObsolete": {"message": "PAC script is obsolete due to URL change. Press the download button above to update."}, "options_group_virtualProfile": {"message": "Virtual Profile"}, "options_virtualProfileTarget": {"message": "Target"}, "options_virtualProfileTargetHelp": {"message": "When this profile is applied, it acts exactly the same as the profile selected below."}, "options_group_virtualProfileReplace": {"message": "Migrate to Virtual Profile"}, "options_virtualProfileReplace": {"message": "Replace target profile"}, "options_virtualProfileReplaceHelp": {"message": "You can migrate existing options to use this virtual profile instead of $PROFILE$. Doing so will update all existing rules concerning $PROFILE$ and point them to this virtual profile, so that their result profile can be controlled here.", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$2"}}}, "options_group_ruleListConfig": {"message": "Rule List Config"}, "options_ruleListFormat": {"message": "Rule List Format"}, "options_group_ruleListResult": {"message": "Rule list result profiles"}, "options_ruleListMatchProfile": {"message": "Match profile"}, "options_ruleListDefaultProfile": {"message": "Default profile"}, "options_group_ruleListUrl": {"message": "Rule List URL"}, "options_ruleListUrlHelp": {"message": "The rule list will be updated from this URL. If it is left blank, the following text will be parsed instead."}, "options_group_ruleListText": {"message": "Rule List Text"}, "options_ruleListLastUpdate": {"message": "Rule list downloaded at $TIME$:", "placeholders": {"_unused_0": {"content": "$0"}, "TIME": {"content": "$1"}}}, "options_ruleListObsolete": {"message": "Rule list is obsolete due to URL change. Press the download button above to update."}, "options_group_switchRules": {"message": "Switch rules"}, "options_sort": {"message": "Sort"}, "options_conditionType": {"message": "Condition Type"}, "options_showConditionTypeHelp": {"message": "Show help"}, "options_conditionDetails": {"message": "Condition Details"}, "options_resultProfile": {"message": "Profile"}, "options_conditionActions": {"message": "Actions"}, "options_addCondition": {"message": "Add condition"}, "options_cloneRule": {"message": "<PERSON><PERSON>"}, "options_ruleNote": {"message": "Note"}, "options_switchAttachedProfileInCondition": {"message": "Rule list rules"}, "options_switchAttachedProfileInConditionDetails": {"message": "(Any request matching the rule list below)"}, "options_switchAttachedProfileInConditionDisabled": {"message": "(Rule list rules are DISABLED)"}, "options_switchDefaultProfile": {"message": "<PERSON><PERSON><PERSON>"}, "options_hostLevelsBetween": {"message": "≤ host levels ≤"}, "options_hourBetween": {"message": "≤ current hour ≤"}, "options_weekDayShort_0": {"message": "Su"}, "options_weekDayShort_1": {"message": "Mo"}, "options_weekDayShort_2": {"message": "Tu"}, "options_weekDayShort_3": {"message": "We"}, "options_weekDayShort_4": {"message": "Th"}, "options_weekDayShort_5": {"message": "Fr"}, "options_weekDayShort_6": {"message": "Sa"}, "options_group_conditionHelp": {"message": "About Condition Types"}, "options_group_attachProfile": {"message": "Import online rule lists"}, "options_attachProfile": {"message": "Add a rule list"}, "options_attachProfileHelp": {"message": "You can reuse an online collection of conditions published by others by adding a rule list."}, "options_modalHeader_welcome": {"message": "Welcome to ZeroOmega"}, "options_welcomeNormal": {"message": "You have successfully installed ZeroOmega, the ultimate proxy switcher."}, "options_welcomeNormalGuide": {"message": "Please tell ZeroOmega about your proxies through the options page. Let's see how."}, "options_welcomeUpgrade": {"message": "You have successfully upgraded to ZeroOmega. Don't panic, your existing options are fully preserved."}, "options_welcomeUpgradeGuide": {"message": "Now let's go through a quick guide of the new options page."}, "options_guideNext": {"message": "Next"}, "options_guideDone": {"message": "Done"}, "options_guideSkip": {"message": "Skip guide"}, "options_modalHeader_applyOptions": {"message": "Apply Options"}, "options_optionsNotSaved": {"message": "Your modifications to the options have not been saved and will be lost if you proceed!"}, "options_applyOptionsRequired": {"message": "Your changes to the options must be applied before you proceed."}, "options_applyOptionsConfirm": {"message": "Do you want to save and apply the options?"}, "options_modalHeader_renameProfile": {"message": "Rename Profile"}, "options_renameProfileName": {"message": "New profile name"}, "options_profileNameConflict": {"message": "A profile with this name already exists."}, "options_profileNameReserved": {"message": "Profile names beginning with double-underscore are reserved."}, "options_profileNameHidden": {"message": "Profiles with names starting with underscore will be hidden on the popup menu. However, they can still be used in places like switch profile results."}, "options_modalHeader_replaceProfile": {"message": "Replace Profile"}, "options_replaceProfile": {"message": "Replace Profile"}, "options_replaceProfileConfirm": {"message": "Do you really want to replace $FromProfile$ with $ToProfile$?", "placeholders": {"_unused_0": {"content": "$0"}, "FromProfile": {"content": "$1"}, "ToProfile": {"content": "$2"}}}, "options_replaceProfileHelp": {"message": "If you proceed, all rules pointing to $FromProfile$ will be updated to use $ToProfile$ instead. Other options, such as startup profile and Quick Switch will also be modified as appropriate. However, the two profile themselves will NOT be changed or deleted.", "placeholders": {"_unused_0": {"content": "$0"}, "FromProfile": {"content": "$1"}, "ToProfile": {"content": "$2"}}}, "options_replaceProfileSuccess": {"message": "Options updated."}, "options_modalHeader_deleteProfile": {"message": "Delete Profile"}, "options_deleteProfileConfirm": {"message": "Do you really want to delete the following profile?"}, "options_modalHeader_cannotDeleteProfile": {"message": "Unable to Delete Profile"}, "options_profileReferredBy": {"message": "This profile cannot be deleted because it is referred by the following profiles:"}, "options_modifyReferringProfiles": {"message": "You must modify these profiles and make them stop referring to this profile before you can delete it."}, "options_profileNameEmpty": {"message": "The name of the profile must not be empty."}, "popup_title": {"message": "ZeroOmega Popup"}, "options_modalHeader_proxyAuth": {"message": "Proxy Authentication"}, "options_proxyAuthUsername": {"message": "Username"}, "options_proxyAuthPassword": {"message": "Password"}, "options_proxyAuthShowPassword": {"message": "Show password"}, "options_proxyAuthHidePassword": {"message": "Hide password"}, "options_proxyAuthNone": {"message": "No Authentication"}, "options_modalHeader_deleteRule": {"message": "Delete Rule"}, "options_deleteRuleConfirm": {"message": "Do you really want to delete the following rule?"}, "options_deleteRule": {"message": "Delete"}, "options_modalHeader_resetRules": {"message": "Reset rules"}, "options_resetRulesConfirm": {"message": "Are you sure to set the result profile of ALL rules to the following profile?"}, "options_resetRules": {"message": "Reset rules"}, "options_resetRules_help": {"message": "Set profile for all rules"}, "options_modalHeader_deleteAttached": {"message": "Remove Rule List"}, "options_deleteAttachedConfirm": {"message": "Do you really want to remove the rule list from the current profile?"}, "options_ruleListLineCount": {"message": "$COUNT$ line(s) of rules", "placeholders": {"_unused_0": {"content": "$0"}, "COUNT": {"content": "$1"}}}, "options_deleteAttached": {"message": "Remove rule list"}, "options_modalHeader_newProfile": {"message": "New Profile"}, "options_newProfileName": {"message": "Profile name"}, "options_profileType": {"message": "Please select the type of the profile:"}, "options_profileTypeFixedProfile": {"message": "Proxy Profile"}, "options_profileDescFixedProfile": {"message": "Tunneling traffic through proxy servers."}, "options_profileTypePacProfile": {"message": "PAC Profile"}, "options_profileDescPacProfile": {"message": "Choosing proxies using an online/local PAC script."}, "options_profileDescMorePacProfile": {"message": "You will only need this if you have a PAC script or a URL to it. Don't try to create one unless you have knowledge about PAC."}, "options_profileTypeSwitchProfile": {"message": "Switch Profile"}, "options_profileDescSwitchProfile": {"message": "Applying different profiles automatically on various conditions such as domains or patterns.\n You can also import rules published online for easier switching. (Replaces AutoSwitch mode + Rule List.)"}, "options_profileTypeRuleListProfile": {"message": "Rule List Profile"}, "options_profileDescRuleListProfile": {"message": "Reusing an online collection of conditions published by others."}, "options_profileTypeVirtualProfile": {"message": "Virtual Profile"}, "options_profileDescVirtualProfile": {"message": "A virtual profile can act as any of the other profiles on demand. It works well with SwitchProfile, allowing you to change the result of multiple conditions by one click."}, "options_createProfile": {"message": "Create"}, "options_modalHeader_resetOptions": {"message": "Reset Options"}, "options_resetOptionsConfirm": {"message": "Do you really want to reset the options? All profiles and settings will be LOST!"}, "options_formInvalid": {"message": "Please correct the errors in this page."}, "options_profileNotFound": {"message": "Profile $PROFILE$ does not exist! The options may be corrupted.", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "options_resetSuccess": {"message": "Options reset."}, "options_saveSuccess": {"message": "Options saved."}, "options_importSuccess": {"message": "Options imported."}, "options_importFormatError": {"message": "Invalid backup file!"}, "options_importDownloadError": {"message": "Error downloading backup file!"}, "options_profileDownloadSuccess": {"message": "Successfully updated profile."}, "options_profileDownloadError": {"message": "Error downloading profile data!"}, "options_profileDownloadError_NetworkError": {"message": "A network error occurred when updating."}, "options_profileDownloadError_HttpError": {"message": "An HTTP error ($STATUS$) occurred when updating.", "placeholders": {"_unused_0": {"content": "$0"}, "STATUS": {"content": "$1"}}}, "options_profileDownloadError_HttpNotFoundError": {"message": "The Profile URL was not found on the server. Please double-check."}, "options_profileDownloadError_HttpServerError": {"message": "The remote server responded with error ($STATUS$) when updating.", "placeholders": {"_unused_0": {"content": "$0"}, "STATUS": {"content": "$1"}}}, "options_profileDownloadError_ContentTypeRejectedError": {"message": "The downloaded data is invalid! You may open the Profile URL in your browser to inspect it."}, "options_downloadProfileNow": {"message": "Download Profile Now"}, "options_guide_fixedProfileStep": {"message": "A <b>Proxy Profile</b> contains settings like server ip &amp; port for proxy.<br>Profiles are the the basic configuration units in ZeroOmega.<br>We have already created an example profile for you. Try opening it."}, "options_guide_fixedServersStep": {"message": "You can fill in your proxy server and port here as you like.<br>ZeroOmega <b>does not come with any proxy servers</b>.<br>Please consult your network provider or proxy software manual if you don't know what should be filled in here."}, "options_guide_autoSwitchProfileStep": {"message": "You can tell ZeroOmega to switch between proxies automatically through the mighty <b>Switch Profile</b>.<br>However, its features cannot be covered in this quick guide.<br>You can open this profile to unlock its power some time later."}, "options_guide_addMoreProfilesStep": {"message": "Need more profiles? You can always add more <b>Proxy, Switch and other profiles</b><br>for all your proxying needs.<br>Enjoy proxying!"}, "options_guide_conditionStep": {"message": "ZeroOmega can apply different profiles to requests based on <b>conditions</b>.<br> For example, the <b>Host wildcard</b> condition allows you to set the profile for all URLs in a domain."}, "options_guide_conditionTypeStep": {"message": "You can use various condition types to match the host or full URL. <br> Click on the question mark to open the type reference."}, "options_guide_conditionProfileStep": {"message": "ZeroOmega applies the selected profile here to <b>any request matching the condition.</b> <br> The special <b>\"[Direct]\" profile</b> will cause the request to be sent without any proxy."}, "options_guide_switchDefaultStep": {"message": "If no condition applies to some request, the \"Default\" profile will be used. <br>Conditions are always considered <b>from top to bottom</b> in order.<br>You can change their order by dragging the sort icon."}, "options_guide_applySwitchProfileStep": {"message": "When you are done setting the switch profile, don't forget to <b>switch to it in the popup menu.</b><br/> The icon will show you the <b>final result</b> profile applied for the current tab. <br/> <b>Hovering</b> on the icon will reveal a tooltip with details."}, "popup_externalProfile": {"message": "(External Profile)"}, "popup_externalProfileName": {"message": "profile name"}, "popup_proxyNotControllable_app": {"message": "The proxy settings are controlled by other app(s) or extension(s). Please disable or uninstall the apps or extensions in conflict."}, "popup_proxyNotControllable_policy": {"message": "The proxy settings are overruled by policies. Please contact your administrator."}, "popup_proxyNotControllable_unknown": {"message": "The proxy settings cannot be controlled. Please check your system and browser settings."}, "popup_proxyNotControllable_disabled": {"message": "The proxy settings are disabled by explicit request from other app(s) or extension(s)."}, "popup_proxyNotControllable_upgrade": {"message": "Proxy settings are now controlled by a newer version of ZeroOmega."}, "popup_proxyNotControllableDetails": {"message": "You cannot switch profiles with ZeroOmega unless you fix the problem above."}, "popup_proxyNotControllableDetails_upgrade": {"message": "You can't enable two (or more) versions of ZeroOmega at the same time. Please disable one of them."}, "popup_proxyNotControllableManage": {"message": "Manage extensions"}, "popup_addConditionTo": {"message": "Add condition to"}, "add_temp_condition": {"message": "Add temp condition"}, "popup_addCondition": {"message": "Add condition"}, "popup_showOptions": {"message": "Options"}, "popup_reload": {"message": "Reload"}, "popup_reportIssues": {"message": "Report issues"}, "popup_errorLog": {"message": "Save error log"}, "popup_requestErrorCount": {"message": "$COUNT$ failed resources", "placeholders": {"_unused_0": {"content": "$0"}, "COUNT": {"content": "$1"}}}, "popup_requestErrorHeading": {"message": "Resources that failed to load"}, "popup_requestErrorWarning": {"message": "A few resources failed to load due to issues with your network, proxy server or the webpage."}, "popup_requestErrorWarningHelp": {"message": "ZeroOmega is just the reporter of these issues, not the cause of them."}, "popup_requestErrorAddCondition": {"message": "You can review the following domains and use proxy for them when appropriate."}, "popup_requestErrorCannotAddCondition": {"message": "You can add switch conditions for them only when using a Switch Profile."}, "popup_configureMonitorWebRequests": {"message": "Configure Network Monitor"}, "options_resultProfileForSelectedDomains": {"message": "Use this profile for all selected domains"}, "options_pac_profile_unsupported_moz": {"message": "PAC Profiles WILL NOT work in Mozilla Firefox due to technical limitations!"}, "popup_issueTemplate": {"message": "\n\n\n<!-- Please write your comment ABOVE this line. -->\nZeroOmega $projectVersion$\n$userAgent$", "placeholders": {"_unused_0": {"content": "$0"}, "projectVersion": {"content": "$1"}, "userAgent": {"content": "$2"}}}, "browserAction_profileDetails_PacProfile": {"message": "(PAC script)"}, "browserAction_profileDetails_SystemProfile": {"message": "(controlled by other extensions or environment)"}, "browserAction_profileDetails_DirectProfile": {"message": "(not using any proxy)"}, "browserAction_profileDetails_SwitchProfile": {"message": "(switching based on conditions)"}, "browserAction_profileDetails_RuleListProfile": {"message": "(switching based on rule list)"}, "browserAction_titleNormal": {"message": "ZeroOmega:: $PROFILE$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "browserAction_titleWithResult": {"message": "ZeroOmega:: $PROFILE$\n$DETAILS$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}, "_unused_2": {"content": "$2"}, "DETAILS": {"content": "$3"}}}, "browserAction_titleNewerOptions": {"message": "ERROR: A newer version of SwitchOmega is required to load the stored options."}, "browserAction_titleOptionError": {"message": "ERROR: The stored options are corrupted. Click here to RESET OPTIONS."}, "browserAction_titleDownloadFail": {"message": "Warning: Failed to download PAC scripts and/or rule lists."}, "browserAction_titleExternalProxy": {"message": "Note: The proxy settings are currently controlled by other app(s)."}, "browserAction_titleInspect": {"message": "[Inspect] $URL$", "placeholders": {"_unused_0": {"content": "$0"}, "URL": {"content": "$1"}}}, "browserAction_defaultRuleDetails": {"message": "(default)"}, "browserAction_directResult": {"message": "DIRECT"}, "browserAction_attachedPrefix": {"message": "(RL) "}, "browserAction_tempRulePrefix": {"message": "(TEMP) "}, "contextMenu_inspectPage": {"message": "Inspect proxy used for this page"}, "contextMenu_inspectFrame": {"message": "Inspect proxy used for this Frame"}, "contextMenu_inspectLink": {"message": "Inspect proxy to be used if this Link is opened"}, "contextMenu_inspectElement": {"message": "Inspect proxy used for this Element"}, "contextMenu_enableQuickSwitch": {"message": "Enable Quick Switch"}, "about_title": {"message": "About"}, "about_app_description": {"message": "A proxy configuration tool"}, "about_version": {"message": "Version $VERSION$", "placeholders": {"_unused_0": {"content": "$0"}, "VERSION": {"content": "$1"}}}, "about_experimental_warning_moz": {"message": "Mozilla Firefox support is highly experimental! If you encounter issues, please report using the buttons below."}, "about_disclaimer_networkService": {"message": "ZeroOmega does not provide proxies, VPNs, or other network services."}, "about_disclaimer_privacy": {"message": "ZeroOmega does not track you or insert ads into webpages. Please see our <a href='https://github.com/FelisCatus/SwitchyOmega/wiki/Privacy#english'>privacy policy</a>."}, "about_help": {"message": "Other questions? Need help with using ZeroOmega? Please see our <a href='https://github.com/FelisCatus/SwitchyOmega/wiki/FAQ'>FAQ</a>."}, "about_copyright": {"message": "Copyright 2012-2017 <a href='https://github.com/FelisCatus/SwitchyOmega/blob/master/AUTHORS'>The SwitchyOmega Authors</a>. All rights reserved.<br>Copyright 2024-2025 <a href='https://github.com/zero-peak/ZeroOmega/graphs/contributors'>The ZeroOmega Authors</a>."}, "about_credits": {"message": "ZeroOmega is made possible by the <a href='https://github.com/zero-peak/ZeroOmega'>ZeroOmega</a> open source project and other <a href='https://github.com/FelisCatus/SwitchyOmega/blob/master/AUTHORS'>open source software</a>."}, "about_license": {"message": "ZeroOmega is <a href='https://www.gnu.org/philosophy/free-sw.en.html'>free software</a> licensed under <a href='https://www.gnu.org/licenses/gpl.html'>GNU General Public License</a> Version 3 or later."}}