"""
DataImpulse代理快速测试脚本
用于验证扩展功能和代理切换性能

使用前请修改下面的用户名和密码
"""

import time
from dataimpulse_browser import DataImpulseBrowser


def main():
    print("🚀 DataImpulse代理快速测试")
    print("=" * 50)
    
    # ⚠️ 请替换为你的DataImpulse凭据
    USERNAME = "your_username"  # 替换为你的用户名
    PASSWORD = "your_password"  # 替换为你的密码
    
    if USERNAME == "your_username" or PASSWORD == "your_password":
        print("❌ 请先在脚本中设置你的DataImpulse用户名和密码！")
        print("   修改 USERNAME 和 PASSWORD 变量")
        return
    
    print(f"📋 测试配置:")
    print(f"   用户名: {USERNAME}")
    print(f"   密码: {'*' * len(PASSWORD)}")
    print(f"   代理1: gw.dataimpulse.com:19965")
    print(f"   代理2: gw.dataimpulse.com:19966")
    print()
    
    # 创建浏览器实例
    print("🔧 创建浏览器实例...")
    browser = DataImpulseBrowser(USERNAME, PASSWORD, "test_browser")
    
    try:
        print("\n" + "="*50)
        print("📊 开始性能测试")
        print("="*50)
        
        # 测试1：初始IP检测
        print("\n🌐 测试1: 初始IP检测")
        print("-" * 30)
        initial_ip = browser.test_ip()
        
        # 测试2：切换到代理1并测试速度
        print("\n⚡ 测试2: 切换到代理1 (19965)")
        print("-" * 30)
        start_time = time.time()
        success1 = browser.switch_to_proxy1()
        switch_time1 = time.time() - start_time
        
        if success1:
            time.sleep(1)  # 等待代理生效
            proxy1_ip = browser.test_ip()
            print(f"📈 代理1切换耗时: {switch_time1:.2f}秒")
            print(f"🔄 IP变化: {initial_ip} → {proxy1_ip}")
        
        # 测试3：切换到代理2并测试速度
        print("\n⚡ 测试3: 切换到代理2 (19966)")
        print("-" * 30)
        start_time = time.time()
        success2 = browser.switch_to_proxy2()
        switch_time2 = time.time() - start_time
        
        if success2:
            time.sleep(1)  # 等待代理生效
            proxy2_ip = browser.test_ip()
            print(f"📈 代理2切换耗时: {switch_time2:.2f}秒")
            print(f"🔄 IP变化: {proxy1_ip if success1 else initial_ip} → {proxy2_ip}")
        
        # 测试4：禁用代理
        print("\n🚫 测试4: 禁用代理")
        print("-" * 30)
        start_time = time.time()
        disable_success = browser.disable_proxy()
        disable_time = time.time() - start_time
        
        if disable_success:
            time.sleep(1)  # 等待代理禁用生效
            final_ip = browser.test_ip()
            print(f"📈 禁用代理耗时: {disable_time:.2f}秒")
            print(f"🔄 IP恢复: {proxy2_ip if success2 else 'Unknown'} → {final_ip}")
        
        # 性能总结
        print("\n" + "="*50)
        print("📊 性能测试总结")
        print("="*50)
        
        if success1:
            print(f"✅ 代理1切换: {switch_time1:.2f}秒")
        else:
            print(f"❌ 代理1切换: 失败")
            
        if success2:
            print(f"✅ 代理2切换: {switch_time2:.2f}秒")
        else:
            print(f"❌ 代理2切换: 失败")
            
        if disable_success:
            print(f"✅ 禁用代理: {disable_time:.2f}秒")
        else:
            print(f"❌ 禁用代理: 失败")
        
        # 计算平均切换时间
        switch_times = []
        if success1:
            switch_times.append(switch_time1)
        if success2:
            switch_times.append(switch_time2)
        if disable_success:
            switch_times.append(disable_time)
        
        if switch_times:
            avg_time = sum(switch_times) / len(switch_times)
            print(f"📈 平均切换时间: {avg_time:.2f}秒")
            
            if avg_time < 1.0:
                print("🎉 切换速度优秀！(< 1秒)")
            elif avg_time < 2.0:
                print("👍 切换速度良好！(< 2秒)")
            else:
                print("⚠️ 切换速度较慢，可能需要优化")
        
        # IP变化总结
        print(f"\n🌐 IP变化记录:")
        print(f"   初始IP: {initial_ip}")
        if success1:
            print(f"   代理1 IP: {proxy1_ip}")
        if success2:
            print(f"   代理2 IP: {proxy2_ip}")
        if disable_success:
            print(f"   最终IP: {final_ip}")
        
        print("\n✨ 测试完成！")
        
        # 使用建议
        print("\n💡 使用建议:")
        print("1. 确保DataImpulse账户有足够的流量")
        print("2. 如果切换失败，检查用户名和密码是否正确")
        print("3. 代理切换后等待1-2秒再进行网络请求")
        print("4. 可以通过浏览器扩展的popup界面手动测试")
        
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        print("请检查:")
        print("1. 扩展文件夹是否存在: dataimpulse_proxy_extension/")
        print("2. DrissionPage是否正确安装")
        print("3. Chrome浏览器是否可用")
        
    finally:
        print(f"\n🔒 清理资源...")
        browser.close()
        print("👋 再见！")


if __name__ == "__main__":
    main()
