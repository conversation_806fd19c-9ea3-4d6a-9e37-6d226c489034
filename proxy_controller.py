"""
代理控制器 - 通过浏览器扩展控制代理轮换的工具
主要功能：
1. 测试插件挂代理功能
2. 程序控制插件轮换代理
3. 支持固定扩展ID和动态代理配置

使用方法:
    controller = ProxyController()
    controller.add_proxy_config("proxy1", "host", port, "name")
    controller.test_ip()  # 测试当前IP
    controller.switch_proxy("proxy1", "username", "password")  # 切换代理
    controller.disable_proxy()  # 禁用代理
"""

import time
import os
from DrissionPage import ChromiumPage, ChromiumOptions
import json


class ProxyController:
    """
    代理控制器
    - 通过浏览器扩展控制代理轮换
    - 固定扩展ID（基于固定文件夹路径）
    - 动态代理配置（通过程序传递）
    - 快速代理切换（0.5-1秒）
    - 内置IP测试功能
    """

    # 固定的扩展路径（确保ID固定）
    EXTENSION_PATH = os.path.join(os.getcwd(), 'universal_proxy_extension')
    
    def __init__(self, browser_id=None):
        """
        初始化代理控制器

        Args:
            browser_id (str, optional): 浏览器标识符
        """
        self.browser_id = browser_id or f"proxy_ctrl_{int(time.time())}"
        self.page = None
        self.extension_id = None
        self.current_proxy = None
        self.proxy_configs = {}  # 本地代理配置缓存

        print(f"🚀 初始化代理控制器 {self.browser_id}")
        self._setup_browser()
    
    def _setup_browser(self):
        """设置浏览器和扩展"""
        start_time = time.time()
        
        # 检查扩展是否存在
        if not os.path.exists(self.EXTENSION_PATH):
            raise FileNotFoundError(f"扩展文件夹不存在: {self.EXTENSION_PATH}")
        
        # 配置浏览器选项
        co = ChromiumOptions()
        co.add_extension(path=self.EXTENSION_PATH)
        co.set_argument('--disable-web-security')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--no-sandbox')
        
        # 启动浏览器
        self.page = ChromiumPage(co)
        
        # 等待扩展加载
        time.sleep(2)
        
        # 获取扩展ID（通过ping测试）
        self._get_extension_id()
        
        elapsed = time.time() - start_time
        print(f"✅ 浏览器 {self.browser_id} 启动完成，耗时: {elapsed:.2f}秒")
        print(f"🎯 扩展ID: {self.extension_id}")
    
    def _get_extension_id(self):
        """获取扩展ID"""
        try:
            result = self.page.run_js("""
                new Promise((resolve, reject) => {
                    // 尝试ping扩展获取ID
                    const timeout = setTimeout(() => {
                        reject('Extension not responding');
                    }, 3000);
                    
                    // 由于扩展路径固定，ID也是固定的
                    // 这里通过ping来验证扩展是否正常工作
                    chrome.runtime.sendMessage(
                        chrome.runtime.id,  // 发送给自己
                        { type: 'ping' },
                        function(response) {
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {
                                reject('Extension error: ' + chrome.runtime.lastError.message);
                            } else {
                                resolve(response);
                            }
                        }
                    );
                })
            """)
            
            if result and result.get('success'):
                self.extension_id = result.get('extensionId', 'unknown')
                print(f"✅ 扩展连接成功，ID: {self.extension_id}")
            else:
                print("⚠️ 扩展ping失败，使用备用方法")
                self.extension_id = "universal-proxy-extension"

        except Exception as e:
            print(f"⚠️ 获取扩展ID失败: {e}")
            self.extension_id = "universal-proxy-extension"
    
    def _send_message_to_extension(self, message):
        """向扩展发送消息"""
        try:
            result = self.page.run_js(f"""
                new Promise((resolve, reject) => {{
                    const timeout = setTimeout(() => {{
                        reject('Extension timeout');
                    }}, 5000);
                    
                    chrome.runtime.sendMessage(
                        '{self.extension_id}',
                        {json.dumps(message)},
                        function(response) {{
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {{
                                reject('Extension error: ' + chrome.runtime.lastError.message);
                            }} else {{
                                resolve(response);
                            }}
                        }}
                    );
                }})
            """)
            return result
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def add_proxy_config(self, config_id, host, port, name=None, scheme='http'):
        """
        添加代理配置

        Args:
            config_id (str): 配置ID
            host (str): 代理服务器地址
            port (int): 代理端口
            name (str, optional): 代理名称
            scheme (str): 代理协议，默认http
        """
        config = {
            'name': name or f'Proxy {config_id}',
            'host': host,
            'port': port,
            'scheme': scheme
        }

        # 本地缓存
        self.proxy_configs[config_id] = config

        # 发送到扩展
        result = self._send_message_to_extension({
            'type': 'addProxyConfig',
            'configId': config_id,
            'config': config
        })

        if result and result.get('success'):
            print(f"✅ [{self.browser_id}] 代理配置 {config_id} 已添加: {host}:{port}")
            return True
        else:
            error_msg = result.get('message', 'Unknown error') if result else 'No response from extension'
            print(f"❌ [{self.browser_id}] 添加代理配置失败: {error_msg}")
            return False

    def test_ip(self):
        """测试当前IP地址"""
        print(f"🌐 [{self.browser_id}] 测试当前IP...")
        start_time = time.time()
        
        try:
            # 方法1：通过扩展测试
            result = self._send_message_to_extension({'type': 'testIP'})
            
            if result and result.get('success'):
                elapsed = time.time() - start_time
                ip = result.get('ip', 'Unknown')
                print(f"✅ [{self.browser_id}] 当前IP: {ip}，耗时: {elapsed:.2f}秒")
                return ip
            
            # 方法2：直接访问API
            print("🔄 使用备用方法测试IP...")
            self.page.get('https://api.ipify.org?format=json', timeout=8)
            ip_data = self.page.json
            ip = ip_data.get('ip', 'Unknown') if ip_data else 'Unknown'
            
            elapsed = time.time() - start_time
            print(f"✅ [{self.browser_id}] 当前IP: {ip}，耗时: {elapsed:.2f}秒")
            return ip
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ [{self.browser_id}] IP测试失败，耗时: {elapsed:.2f}秒，错误: {e}")
            return None
    
    def switch_proxy(self, config_id, username, password):
        """
        切换到指定代理

        Args:
            config_id (str): 代理配置ID
            username (str): 代理用户名
            password (str): 代理密码
        """
        if config_id not in self.proxy_configs:
            print(f"❌ 未知的代理配置: {config_id}")
            print(f"   可用配置: {list(self.proxy_configs.keys())}")
            return False

        proxy_config = self.proxy_configs[config_id]
        print(f"⚡ [{self.browser_id}] 切换到 {proxy_config['name']}")
        start_time = time.time()

        try:
            result = self._send_message_to_extension({
                'type': 'switchProxy',
                'configId': config_id,
                'username': username,
                'password': password
            })

            elapsed = time.time() - start_time

            if result and result.get('success'):
                self.current_proxy = config_id
                print(f"✅ [{self.browser_id}] {result.get('message')}，耗时: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ [{self.browser_id}] 代理切换失败，耗时: {elapsed:.2f}秒")
                print(f"   错误: {result.get('message', 'Unknown error')}")
                return False

        except Exception as e:
            elapsed = time.time() - start_time
            print(f"💥 [{self.browser_id}] 代理切换异常，耗时: {elapsed:.2f}秒，错误: {e}")
            return False
    
    def disable_proxy(self):
        """禁用代理"""
        print(f"🚫 [{self.browser_id}] 禁用代理...")
        start_time = time.time()
        
        try:
            result = self._send_message_to_extension({'type': 'disableProxy'})
            
            elapsed = time.time() - start_time
            
            if result and result.get('success'):
                self.current_proxy = None
                print(f"✅ [{self.browser_id}] 代理已禁用，耗时: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ [{self.browser_id}] 禁用代理失败，耗时: {elapsed:.2f}秒")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"💥 [{self.browser_id}] 禁用代理异常，耗时: {elapsed:.2f}秒，错误: {e}")
            return False
    
    def get(self, url, **kwargs):
        """访问网页"""
        return self.page.get(url, **kwargs)
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            print(f"🔒 关闭浏览器 {self.browser_id}")
            self.page.quit()


# 使用示例
if __name__ == "__main__":
    # 测试凭据（可替换为任何代理服务器）
    USERNAME = "0465846b31e1f583b17c__cr.us"
    PASSWORD = "16af34bf75f0573a"

    print("🚀 代理控制器基础测试")

    # 创建代理控制器实例
    controller = ProxyController("test_controller")

    try:
        # 添加代理配置
        print("\n1. 添加代理配置:")
        controller.add_proxy_config("proxy1", "gw.dataimpulse.com", 19965, "Test Proxy 1")
        controller.add_proxy_config("proxy2", "gw.dataimpulse.com", 19966, "Test Proxy 2")

        # 测试当前IP
        print("\n2. 测试初始IP:")
        controller.test_ip()

        # 切换到代理1
        print("\n3. 切换到代理1:")
        controller.switch_proxy("proxy1", USERNAME, PASSWORD)
        time.sleep(2)
        controller.test_ip()

        # 切换到代理2
        print("\n4. 切换到代理2:")
        controller.switch_proxy("proxy2", USERNAME, PASSWORD)
        time.sleep(2)
        controller.test_ip()

        # 禁用代理
        print("\n5. 禁用代理:")
        controller.disable_proxy()
        time.sleep(2)
        controller.test_ip()

        print("\n✨ 测试完成！")

    finally:
        controller.close()
