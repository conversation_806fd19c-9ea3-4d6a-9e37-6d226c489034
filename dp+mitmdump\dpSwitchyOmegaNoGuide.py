#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SwitchyOmega无教程解决方案
通过预设扩展状态绕过首次运行教程
"""

import os
import time
import tempfile
import json
from DrissionPage import ChromiumPage, ChromiumOptions


class SwitchyOmegaNoGuide:
    """SwitchyOmega无教程解决方案"""
    
    def __init__(self):
        self.page = None
        self.temp_dir = None
        
        # 获取扩展路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        
        self.switchyomega_path = os.path.join(script_dir, "chromium-release")
        self.ua_patch_path = os.path.join(project_root, "cloudflare_ua_patch")
        
        # 正确的SwitchyOmega扩展ID
        self.extension_id = "fjplonlgeikcikbiffklcbdikcaienob"
        
        # 代理配置
        self.proxy_config = {
            "host": "gw.dataimpulse.com",
            "port": "19965", 
            "username": "0465846b31e1f583b17c__cr.us",
            "password": "16af34bf75f0573a"
        }
    
    def setup_user_data_directory(self):
        """设置用户数据目录并预配置扩展状态"""
        print("🔧 设置用户数据目录...")
        
        # 创建临时用户数据目录
        self.temp_dir = tempfile.mkdtemp(prefix="switchyomega_noguide_")
        print(f"📁 用户数据目录: {self.temp_dir}")
        
        # 创建必要的目录结构
        local_storage_dir = os.path.join(self.temp_dir, "Default", "Local Storage", "leveldb")
        os.makedirs(local_storage_dir, exist_ok=True)
        
        # 创建扩展状态目录
        extension_state_dir = os.path.join(self.temp_dir, "Default", "Extension State")
        os.makedirs(extension_state_dir, exist_ok=True)
        
        # 预设SwitchyOmega的基本配置，跳过首次运行
        preferences_path = os.path.join(self.temp_dir, "Default", "Preferences")
        
        # 基本的Chrome preferences，包含扩展状态
        preferences = {
            "extensions": {
                "settings": {
                    self.extension_id: {
                        "active_permissions": {
                            "api": ["proxy", "tabs", "alarms", "storage", "unlimitedStorage", 
                                   "webRequest", "webRequestAuthProvider", "contextMenus"],
                            "explicit_host": ["<all_urls>"]
                        },
                        "creation_flags": 1,
                        "from_webstore": False,
                        "granted_permissions": {
                            "api": ["proxy", "tabs", "alarms", "storage", "unlimitedStorage", 
                                   "webRequest", "webRequestAuthProvider", "contextMenus"],
                            "explicit_host": ["<all_urls>"]
                        },
                        "install_time": "13000000000000000",
                        "location": 4,
                        "manifest": {
                            "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA",
                            "name": "Proxy SwitchyOmega 3 (ZeroOmega)",
                            "version": "3.4.3"
                        },
                        "path": self.switchyomega_path,
                        "state": 1,
                        "was_installed_by_default": False,
                        "was_installed_by_oem": False
                    }
                }
            },
            "profile": {
                "default_content_setting_values": {},
                "default_content_settings": {},
                "content_settings": {}
            }
        }
        
        # 写入preferences文件
        with open(preferences_path, 'w', encoding='utf-8') as f:
            json.dump(preferences, f, indent=2)
        
        print("✅ 用户数据目录配置完成")
        return True
    
    def create_browser(self):
        """创建浏览器"""
        print("🚀 创建浏览器实例...")
        
        # 先设置用户数据目录
        if not self.setup_user_data_directory():
            return False
        
        options = ChromiumOptions()
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")
        
        # 完全复制dpMitmdumpTest.py的启动参数
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768',
            '--disable-extensions-file-access-check',  # 允许扩展访问
            '--disable-web-security'  # 禁用web安全检查
        ]
        
        [options.set_argument(arg) for arg in args]
        
        # 设置用户数据目录
        options.set_user_data_path(self.temp_dir)
        
        # 加载扩展
        if os.path.exists(self.ua_patch_path):
            print(f"✅ 加载cloudflare_ua_patch: {self.ua_patch_path}")
            options.add_extension(self.ua_patch_path)
        
        if os.path.exists(self.switchyomega_path):
            print(f"✅ 加载SwitchyOmega: {self.switchyomega_path}")
            options.add_extension(self.switchyomega_path)
        else:
            print("❌ SwitchyOmega扩展未找到")
            return False
        
        options.auto_port()
        
        try:
            self.page = ChromiumPage(options)
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def preset_extension_state(self):
        """预设扩展状态，跳过首次运行"""
        try:
            print("🔧 预设扩展状态...")
            
            # 等待扩展加载
            time.sleep(3)
            
            # 通过JavaScript设置扩展状态
            js_code = f"""
            // 设置firstRun状态为空，跳过首次运行教程
            chrome.storage.local.set({{
                'firstRun': '',
                'web.switchGuide': 'shown',
                'options': {{
                    '+system': {{
                        'profileType': 'SystemProfile',
                        'name': 'system'
                    }},
                    '+direct': {{
                        'profileType': 'DirectProfile', 
                        'name': 'direct'
                    }},
                    'proxy': {{
                        'profileType': 'FixedProfile',
                        'name': 'proxy',
                        'bypassList': [{{
                            'conditionType': 'BypassCondition',
                            'pattern': '<local>'
                        }}],
                        'proxyForHttp': {{
                            'scheme': 'https',
                            'host': '{self.proxy_config["host"]}',
                            'port': {self.proxy_config["port"]}
                        }},
                        'proxyForHttps': {{
                            'scheme': 'https', 
                            'host': '{self.proxy_config["host"]}',
                            'port': {self.proxy_config["port"]}
                        }},
                        'proxyForFtp': {{
                            'scheme': 'https',
                            'host': '{self.proxy_config["host"]}',
                            'port': {self.proxy_config["port"]}
                        }},
                        'fallbackProxy': {{
                            'scheme': 'https',
                            'host': '{self.proxy_config["host"]}',
                            'port': {self.proxy_config["port"]}
                        }},
                        'auth': {{
                            'fallbackProxy': {{
                                'username': '{self.proxy_config["username"]}',
                                'password': '{self.proxy_config["password"]}'
                            }}
                        }}
                    }},
                    '-startupProfileName': '',
                    '-quickSwitchProfiles': [],
                    '-revertProxyChanges': true,
                    '-confirmDeletion': true,
                    '-refreshOnProfileChange': true,
                    '-enableQuickSwitch': false
                }}
            }}, function() {{
                console.log('SwitchyOmega state preset completed');
            }});
            """
            
            # 访问扩展页面并执行JavaScript
            extension_url = f"chrome-extension://{self.extension_id}/options.html"
            self.page.get(extension_url)
            time.sleep(2)
            
            # 执行预设脚本
            self.page.run_js(js_code)
            time.sleep(2)
            
            print("✅ 扩展状态预设完成")
            return True
            
        except Exception as e:
            print(f"⚠️ 预设扩展状态遇到问题: {e}")
            print("💡 继续使用手动配置方式")
            return False
    
    def configure_proxy_simple(self):
        """简单配置代理"""
        try:
            print("🔧 配置代理...")
            
            # 直接访问popup进行配置
            popup_url = f"chrome-extension://{self.extension_id}/popup.html"
            print(f"📖 访问popup: {popup_url}")
            self.page.get(popup_url)
            time.sleep(2)
            
            # 查找并点击proxy配置
            proxy_links = self.page.eles('x://a[contains(@title, "proxy") or contains(text(), "proxy")]')
            if proxy_links:
                print("✅ 找到proxy配置，点击激活...")
                proxy_links[0].click()
                time.sleep(2)
                print("✅ 代理已激活")
                return True
            else:
                print("⚠️ 未找到proxy配置，可能需要手动配置")
                return False
            
        except Exception as e:
            print(f"⚠️ 配置过程遇到问题: {e}")
            return False
    
    def test_proxy(self):
        """测试代理"""
        try:
            print("\n🔍 测试代理连接...")
            
            # 测试IP
            self.page.get("https://api.ipify.org/")
            time.sleep(3)
            
            ip_content = self.page.html
            import re
            ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
            current_ip = ip_match.group() if ip_match else "未知"
            
            print(f"🌐 当前IP: {current_ip}")
            
            # 检查是否是代理IP
            if current_ip != "**************":
                print("✅ 代理IP生效！")
                return True
            else:
                print("⚠️ 仍然是本地IP")
                return False
                
        except Exception as e:
            print(f"⚠️ 测试遇到问题: {e}")
            return False
    
    def run(self):
        """运行无教程配置流程"""
        print("🎮 SwitchyOmega无教程解决方案")
        print("🎯 通过预设扩展状态绕过首次运行教程")
        print()
        
        if not self.create_browser():
            return
        
        print("⏳ 等待扩展加载...")
        time.sleep(5)
        
        # 预设扩展状态
        if self.preset_extension_state():
            print("\n✅ 扩展状态预设成功！")
        
        # 配置代理
        if self.configure_proxy_simple():
            print("\n✅ 代理配置完成！")
            
            # 测试代理
            if self.test_proxy():
                print("\n🎉 代理测试成功！")
                print("💡 现在可以:")
                print("  🌐 使用代理访问网站")
                print("  🛡️ 绕过Cloudflare保护")
                print("  🔄 动态切换代理")
                print("  🚫 无教程弹窗干扰")
            else:
                print("\n⚠️ 代理可能未完全生效")
        else:
            print("\n⚠️ 代理配置需要手动完成")
        
        print("\n🔚 按Enter关闭...")
        input("⏳ 按Enter关闭浏览器...")
        
        if self.page:
            self.page.quit()
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            self.page.quit()


def main():
    """主函数"""
    try:
        noguide = SwitchyOmegaNoGuide()
        noguide.run()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        if 'noguide' in locals():
            noguide.close()


if __name__ == "__main__":
    main()
