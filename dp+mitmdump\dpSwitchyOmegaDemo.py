#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SwitchyOmega代理配置 - 基于成功demo
完全按照主人提供的成功代码重写
"""

import os
import time
from DrissionPage import ChromiumPage, ChromiumOptions


class SwitchyOmegaDemo:
    """基于成功demo的SwitchyOmega配置器"""
    
    def __init__(self):
        self.page = None
        
        # 获取扩展路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        
        self.switchyomega_path = os.path.join(script_dir, "chromium-release")
        self.ua_patch_path = os.path.join(project_root, "cloudflare_ua_patch")
        
        # 正确的SwitchyOmega扩展ID
        self.extension_id = "fjplonlgeikcikbiffklcbdikcaienob"
        
        # 代理配置
        self.proxy_config = {
            "host": "gw.dataimpulse.com",
            "port": "19965", 
            "username": "0465846b31e1f583b17c__cr.us",
            "password": "16af34bf75f0573a"
        }
    
    def create_browser(self):
        """创建浏览器（完全基于dpMitmdumpTest.py配置）"""
        print("🚀 创建浏览器实例...")
        
        options = ChromiumOptions()
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")
        
        # 完全复制dpMitmdumpTest.py的启动参数
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768'
        ]
        
        [options.set_argument(arg) for arg in args]
        
        # 加载扩展
        if os.path.exists(self.ua_patch_path):
            print(f"✅ 加载cloudflare_ua_patch: {self.ua_patch_path}")
            options.add_extension(self.ua_patch_path)
        
        if os.path.exists(self.switchyomega_path):
            print(f"✅ 加载SwitchyOmega: {self.switchyomega_path}")
            options.add_extension(self.switchyomega_path)
        else:
            print("❌ SwitchyOmega扩展未找到")
            return False
        
        # 设置用户数据目录避免冲突
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="switchyomega_")
        options.set_user_data_path(temp_dir)

        options.auto_port()

        try:
            self.page = ChromiumPage(options)
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            print("💡 提示: 请确保没有其他Chromium实例在运行")
            return False
    
    def handle_welcome_popup(self):
        """处理欢迎弹窗"""
        try:
            # 查找Skip guide按钮
            skip_button = self.page.ele('x://button[contains(text(), "Skip guide")]', timeout=2)
            if skip_button:
                skip_button.click()
                time.sleep(1)
                print("✅ 已跳过欢迎向导")
                return True
            
            # 查找Next按钮
            next_button = self.page.ele('x://button[contains(text(), "Next")]', timeout=1)
            if next_button:
                next_button.click()
                time.sleep(1)
                print("✅ 点击了Next")
                return True
            
            return True
        except:
            return True
    
    def set_switchy_omega(self):
        """完全按照demo的set_switchy_omega函数"""
        try:
            print("🔧 开始配置SwitchyOmega...")
            
            # 1. 访问proxy配置页面（使用正确的扩展ID）
            proxy_url = f"chrome-extension://{self.extension_id}/options.html#!/profile/proxy"
            print(f"📖 访问: {proxy_url}")
            self.page.get(proxy_url)
            time.sleep(3)
            
            # 处理欢迎弹窗
            self.handle_welcome_popup()
            
            # 2. 配置服务器地址（完全按照demo）
            print("🔧 配置服务器地址...")
            host_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].host"]', timeout=5)
            if host_input:
                host_input.clear().input(self.proxy_config['host'])
                print(f"✅ 服务器: {self.proxy_config['host']}")
            else:
                print("❌ 未找到服务器输入框")
                return False
            
            # 3. 配置端口（完全按照demo）
            print("🔧 配置端口...")
            port_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].port"]', timeout=5)
            if port_input:
                port_input.clear().input(self.proxy_config['port'])
                print(f"✅ 端口: {self.proxy_config['port']}")
            else:
                print("❌ 未找到端口输入框")
                return False
            
            # 4. 配置认证（完全按照demo的步骤和方法）
            print("🔧 配置认证...")
            auth_btn = self.page.ele('x://button[@ng-click="editProxyAuth(scheme)"]', timeout=5)
            if auth_btn:
                auth_btn.click(by_js=True)
                time.sleep(1)
                
                # 用户名（完全按照demo）
                username_input = self.page.ele('x://input[@ng-model="model"]', timeout=5)
                if username_input:
                    username_input.clear().click(by_js=True).input(self.proxy_config['username'])
                    print("✅ 用户名已设置")
                
                # 密码（完全按照demo）
                password_input = self.page.ele('x://input[@ng-model="auth.password"]', timeout=5)
                if password_input:
                    password_input.clear().click(by_js=True).input(self.proxy_config['password'])
                    print("✅ 密码已设置")
                
                # 保存认证（完全按照demo）
                save_auth_btn = self.page.ele('x://button[@ng-disabled="!authForm.$valid"]', timeout=5)
                if save_auth_btn:
                    save_auth_btn.click(by_js=True)
                    time.sleep(1)
                    print("✅ 认证已保存")
            
            # 5. 应用配置（完全按照demo）
            print("🔧 应用配置...")
            apply_btn = self.page.ele('x://a[@ng-click="applyOptions()"]', timeout=5)
            if apply_btn:
                apply_btn.click(by_js=True)
                time.sleep(2)
                print("✅ 配置已应用")
            else:
                print("❌ 未找到应用按钮")
                return False
            
            # 6. 切换到UI页面（这是关键步骤！）
            print("🔧 切换到UI页面激活代理...")
            ui_url = f"chrome-extension://{self.extension_id}/options.html#!/ui"
            print(f"📖 访问: {ui_url}")
            self.page.get(ui_url)
            time.sleep(2)
            
            # 处理可能的弹窗
            self.handle_welcome_popup()
            
            # 7. 激活代理（关键：选择proxy profile）
            print("🔧 激活代理...")
            dropdown_btn = self.page.ele('x://button[@class="btn btn-default dropdown-toggle"]', timeout=5)
            if dropdown_btn:
                dropdown_btn.click(by_js=True)
                time.sleep(1)
                print("✅ 打开代理选择菜单")

                # 8. 选择proxy配置（这是关键步骤！）
                print("🔧 选择proxy配置...")
                proxy_option = self.page.ele('x://a[contains(text(), "proxy")]', timeout=5)
                if not proxy_option:
                    # 尝试其他可能的选择器
                    proxy_option = self.page.ele('x://li[contains(text(), "proxy")]', timeout=3)
                if not proxy_option:
                    proxy_option = self.page.ele('x://*[contains(text(), "proxy")]', timeout=3)

                if proxy_option:
                    proxy_option.click(by_js=True)
                    time.sleep(2)
                    print("✅ 已选择proxy配置")
                    return True
                else:
                    print("❌ 未找到proxy选项，尝试手动查找...")
                    # 列出所有可用选项
                    options = self.page.eles('x://ul[@class="dropdown-menu"]//a', timeout=3)
                    if options:
                        print("📋 可用选项:")
                        for i, option in enumerate(options):
                            text = option.text
                            print(f"  {i}: {text}")
                            if "proxy" in text.lower():
                                option.click(by_js=True)
                                time.sleep(2)
                                print(f"✅ 选择了: {text}")
                                return True

                    print("❌ 未找到proxy选项")
                    return False
            else:
                print("❌ 未找到激活按钮")
                return False
            
        except Exception as e:
            print(f"❌ 配置失败: {e}")
            return False
    
    def test_proxy(self):
        """测试代理是否生效"""
        try:
            print("\n🔍 测试代理连接...")
            
            # 测试IP
            self.page.get("https://api.ipify.org/")
            time.sleep(3)
            
            ip_content = self.page.html
            import re
            ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
            current_ip = ip_match.group() if ip_match else "未知"
            
            print(f"🌐 当前IP: {current_ip}")
            
            # 测试X.com访问
            print("🔍 测试X.com访问...")
            self.page.get("https://x.com/account/access")
            time.sleep(3)
            
            if "x.com" in self.page.url:
                print("✅ X.com访问成功")
                return True
            else:
                print("⚠️ X.com访问可能有问题")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def run(self):
        """运行代理配置"""
        print("🎮 SwitchyOmega代理配置 - 基于成功demo")
        print("🎯 完全按照主人提供的成功代码执行")
        print()
        
        if not self.create_browser():
            return
        
        print("⏳ 等待扩展加载...")
        time.sleep(5)
        
        if self.set_switchy_omega():
            print("\n✅ 代理配置完成！")
            
            if self.test_proxy():
                print("\n🎉 代理测试成功！")
                print("💡 现在可以:")
                print("  🌐 使用代理访问网站")
                print("  🛡️ 绕过Cloudflare保护")
                print("  🔄 动态切换代理")
            else:
                print("\n⚠️ 代理测试未完全通过，但配置已完成")
        else:
            print("\n❌ 代理配置失败")
        
        print("\n🔚 按Enter关闭...")
        input("⏳ 按Enter关闭浏览器...")
        
        if self.page:
            self.page.quit()
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            self.page.quit()


def main():
    """主函数"""
    try:
        demo = SwitchyOmegaDemo()
        demo.run()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        if 'demo' in locals():
            demo.close()


if __name__ == "__main__":
    main()
