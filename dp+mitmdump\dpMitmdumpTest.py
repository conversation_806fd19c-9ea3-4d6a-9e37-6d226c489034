#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage + mitmdump 上下游认证代理完整方案
实现浏览器通过本地代理访问上游认证代理服务器
"""

import atexit
import os
import re
import socket
import subprocess
import time

from DrissionPage import ChromiumPage, ChromiumOptions


class ProxyManager:
    """代理管理器"""

    def __init__(self, upstream_proxy_url, local_port=5858):
        """
        初始化代理管理器

        Args:
            upstream_proxy_url (str): 上游代理URL，格式: *********************:port
            local_port (int): 本地mitmdump监听端口
        """
        self.upstream_proxy_url = upstream_proxy_url
        self.local_port = local_port
        self.mitmdump_process = None
        # 获取当前脚本所在目录
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.proxy_extension_path = os.path.join(script_dir, "ProxyExt")  # 固定代理扩展路径

        # 解析上游代理信息
        self._parse_upstream_proxy()

    def _parse_upstream_proxy(self):
        """解析上游代理URL"""
        pattern = r'^https?://([^:]+):([^@]+)@([^:]+):(\d+)$'
        match = re.match(pattern, self.upstream_proxy_url)

        if not match:
            raise ValueError(f"Invalid proxy URL format: {self.upstream_proxy_url}")

        self.upstream_user, self.upstream_pass, self.upstream_host, self.upstream_port = match.groups()
        self.upstream_port = int(self.upstream_port)

        print(f"解析上游代理: {self.upstream_host}:{self.upstream_port}")

    def start_mitmdump(self):
        """启动mitmdump代理服务"""
        # 使用经过验证的HTTPS上游代理配置，添加证书处理
        cmd = [
            'mitmdump',
            '--mode', f'upstream:https://{self.upstream_host}:{self.upstream_port}',
            '--upstream-auth', f'{self.upstream_user}:{self.upstream_pass}',
            '--listen-port', str(self.local_port),
            '--set', 'confdir=~/.mitmproxy',  # 设置配置目录
            '--set', 'upstream_cert=false',  # 忽略上游代理的SSL证书验证
            '--set', 'ssl_insecure=true',  # 忽略SSL验证
            '--set', 'web_open_browser=false',  # 不自动打开浏览器
            '--quiet'  # 减少输出
        ]

        print(f"启动mitmdump: {' '.join(cmd)}")

        try:
            self.mitmdump_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )

            # 注册退出时清理
            atexit.register(self._cleanup)

            # 等待mitmdump启动
            self._wait_for_proxy_ready()

            print(f"mitmdump已启动，PID: {self.mitmdump_process.pid}")
            return True

        except FileNotFoundError:
            print("错误: mitmdump未找到，请确保已安装mitmproxy")
            return False
        except Exception as e:
            print(f"启动mitmdump失败: {e}")
            return False

    def _wait_for_proxy_ready(self, timeout=30):
        """等待代理服务就绪"""
        print(f"等待代理服务启动 (端口 {self.local_port})...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('127.0.0.1', self.local_port))
                    if result == 0:
                        print("代理服务已就绪")
                        return True
            except:
                pass
            time.sleep(1)

        raise TimeoutError(f"代理服务启动超时 (>{timeout}s)")

    def _cleanup(self):
        """清理资源"""
        if self.mitmdump_process:
            try:
                self.mitmdump_process.terminate()
                self.mitmdump_process.wait(timeout=5)
                print("mitmdump进程已终止")
            except:
                try:
                    self.mitmdump_process.kill()
                    print("mitmdump进程已强制终止")
                except:
                    pass

    def get_extension_path(self):
        """获取代理扩展路径"""
        return self.proxy_extension_path


class ProxyBrowser:
    """使用代理的浏览器封装"""

    def __init__(self, proxy_manager):
        """
        初始化代理浏览器

        Args:
            proxy_manager (ProxyManager): 代理管理器实例
        """
        self.proxy_manager = proxy_manager
        self.page = None

    def create_browser(self, headless=False, user_agent=None):
        """创建配置好代理的浏览器实例"""
        options = ChromiumOptions()
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")

        # 启动参数
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768'
        ]

        # 无头模式
        if headless:
            args.append('--headless')

        # 自定义User-Agent
        if user_agent:
            args.append(f'--user-agent={user_agent}')

        # 应用所有参数
        [options.set_argument(arg) for arg in args]

        # 添加代理扩展
        extension_path = self.proxy_manager.get_extension_path()
        print(f"加载代理扩展: {extension_path}")
        options.add_extension(extension_path)


        # 自动分配端口
        options.auto_port()

        try:
            self.page = ChromiumPage(options)
            print("浏览器启动成功，代理扩展已加载")

            # 快速验证
            print("验证代理...")
            time.sleep(2)
            self._check_proxy_connection()

            return self.page
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            raise

    def _check_proxy_connection(self):
        """快速检查代理"""
        try:
            import requests
            proxies = {'http': 'http://127.0.0.1:5858', 'https': 'http://127.0.0.1:5858'}
            response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=5)
            proxy_ip = response.json().get('origin', 'unknown')
            print(f"代理IP: {proxy_ip} {'OK' if not proxy_ip.startswith('103.151.172') else 'WARN'}")
        except Exception as e:
            print(f"代理检查失败: {e}")

    def test_proxy(self):
        """测试代理是否工作"""
        if not self.page:
            raise ValueError("浏览器未初始化")

        test_urls = [
            "http://httpbin.org/ip",  # 显示IP地址
            "https://httpbin.org/headers",  # 显示请求头
            "https://x.com/account/access"  # X.com测试页面
        ]

        results = {}

        for url in test_urls:
            try:
                print(f"\n测试: {url}")
                self.page.get(url)
                time.sleep(2)
                content = self.page.html

                # IP检查
                if "httpbin.org/ip" in url:
                    import re
                    ip_match = re.search(r'"origin":\s*"([^"]+)"', content)
                    if ip_match:
                        ip = ip_match.group(1)
                        print(f"  IP: {ip} {'OK' if not ip.startswith('103.151.172') else 'WARN'}")

                results[url] = {"status": "success", "title": self.page.title, "content_length": len(content)}
                print(f"OK {self.page.title} ({len(content)}字符)")

                # X.com特殊处理
                if "x.com" in url:
                    print(f"\nX.com已加载，当前: {self.page.url}")
                    input("完成手动操作后按Enter...")

                    print("导出数据...")
                    cookie_data = self.export_cookies_and_ua()

                    if cookie_data:
                        print("原始代理请求...")
                        response = self.test_with_exported_data(cookie_data, url)
                        print("完成" if response else "失败")
                    else:
                        print("导出失败")

            except Exception as e:
                results[url] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"✗ {url} - 失败: {e}")

        return results

    def export_cookies_and_ua(self):
        """导出当前页面的Cookie和User-Agent"""
        if not self.page:
            raise ValueError("浏览器未初始化")

        try:
            # 获取所有Cookie
            cookies = self.page.cookies()

            # 获取User-Agent
            user_agent = self.page.run_js("return navigator.userAgent;")

            # 格式化Cookie为字符串
            cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

            print(f"\n导出数据: {len(cookies)}个Cookie, UA长度: {len(user_agent)}")

            return {
                'cookies': cookies,
                'cookie_string': cookie_str,
                'user_agent': user_agent,
                'url': self.page.url
            }

        except Exception as e:
            print(f"❌ 导出Cookie和UA失败: {e}")
            return None

    def test_with_exported_data(self, cookie_data, target_url="https://x.com/account/access"):
        """使用导出的Cookie和UA通过原始代理请求页面"""
        if not cookie_data:
            print("❌ 没有Cookie数据")
            return

        print(f"\n使用原始代理请求: {target_url}")

        try:
            import requests

            # 构建请求头
            headers = {
                'User-Agent': cookie_data['user_agent'],
                'Cookie': cookie_data['cookie_string'],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none'
            }

            # 使用原始代理（直接连接上游代理，不通过mitmdump）
            original_proxy_url = "https://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"
            proxies = {
                'http': original_proxy_url,
                'https': original_proxy_url
            }

            print(f"原始代理: gw.dataimpulse.com:19965, Cookie: {len(headers['Cookie'])}字符")
            response = requests.get(
                target_url,
                headers=headers,
                proxies=proxies,
                timeout=30,
                verify=False,  # 忽略SSL证书验证
                allow_redirects=True
            )

            print(f"状态码: {response.status_code}, 内容: {len(response.text)}字符")

            # 简单内容检查
            content = response.text
            if "Just a moment" in content:
                print("Cloudflare验证页面")
            elif "Log in to X" in content:
                print("X登录页面")
            elif response.status_code == 200:
                print("请求成功")

            return response

        except Exception as e:
            print(f"❌ 请求失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def close(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                print("浏览器已关闭")
            except:
                pass


def main(proxy_url):
    """主函数示例"""
    # 配置上游代理 (请替换为真实的代理信息)
    upstream_proxy = proxy_url

    try:
        # 启动代理
        proxy_manager = ProxyManager(upstream_proxy, local_port=5858)
        if not proxy_manager.start_mitmdump():
            print("代理启动失败")
            return

        # 创建浏览器并测试
        browser = ProxyBrowser(proxy_manager)
        browser.create_browser(headless=False)

        print("\n代理测试")
        test_results = browser.test_proxy()

        print("\n结果")
        for url, result in test_results.items():
            print(f"{url}: {result['status']}")

        input("\n按Enter关闭...")

    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"运行出错: {e}")
    finally:
        # 清理资源
        if 'browser' in locals():
            browser.close()


if __name__ == "__main__":
    # 使用示例
    proxy_url = "https://0465846b31e1f583b17c__cr.us:<EMAIL>:19965"

    main(proxy_url)
