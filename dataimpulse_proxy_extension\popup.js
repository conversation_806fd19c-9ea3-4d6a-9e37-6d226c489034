// DataImpulse代理扩展 - Popup界面脚本
console.log('🎯 DataImpulse Popup已加载');

// DOM元素
const currentIPEl = document.getElementById('currentIP');
const testIPBtn = document.getElementById('testIPBtn');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const proxy1Btn = document.getElementById('proxy1Btn');
const proxy2Btn = document.getElementById('proxy2Btn');
const disableBtn = document.getElementById('disableBtn');
const statusEl = document.getElementById('status');

// 显示状态信息
function showStatus(message, type = 'success') {
  statusEl.textContent = message;
  statusEl.className = `status ${type}`;
  
  // 3秒后清除状态
  setTimeout(() => {
    statusEl.textContent = '准备就绪';
    statusEl.className = 'status';
  }, 3000);
}

// 测试IP地址
async function testIP() {
  testIPBtn.disabled = true;
  testIPBtn.textContent = '🔄 测试中...';
  currentIPEl.textContent = '检测中...';
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'testIP'
    });
    
    if (response && response.success) {
      currentIPEl.textContent = response.ip;
      showStatus(`IP检测成功，耗时: ${response.elapsed}ms`, 'success');
    } else {
      currentIPEl.textContent = '检测失败';
      showStatus('IP检测失败: ' + (response?.error || '未知错误'), 'error');
    }
  } catch (error) {
    currentIPEl.textContent = '检测失败';
    showStatus('IP检测异常: ' + error.message, 'error');
  }
  
  testIPBtn.disabled = false;
  testIPBtn.textContent = '🔍 测试IP';
}

// 切换到DataImpulse代理
async function switchToProxy(proxyKey) {
  const username = usernameInput.value.trim();
  const password = passwordInput.value.trim();
  
  if (!username || !password) {
    showStatus('请输入用户名和密码', 'error');
    return;
  }
  
  const button = proxyKey === 'proxy1' ? proxy1Btn : proxy2Btn;
  const originalText = button.textContent;
  
  button.disabled = true;
  button.textContent = '🔄 切换中...';
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'switchDataImpulse',
      proxyKey: proxyKey,
      username: username,
      password: password
    });
    
    if (response && response.success) {
      showStatus(`${response.message}，耗时: ${response.elapsed}ms`, 'success');
      
      // 自动测试新IP
      setTimeout(testIP, 1000);
    } else {
      showStatus('代理切换失败: ' + (response?.message || '未知错误'), 'error');
    }
  } catch (error) {
    showStatus('代理切换异常: ' + error.message, 'error');
  }
  
  button.disabled = false;
  button.textContent = originalText;
}

// 禁用代理
async function disableProxy() {
  disableBtn.disabled = true;
  disableBtn.textContent = '🔄 禁用中...';
  
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'disableProxy'
    });
    
    if (response && response.success) {
      showStatus(`代理已禁用，耗时: ${response.elapsed}ms`, 'success');
      
      // 自动测试新IP
      setTimeout(testIP, 1000);
    } else {
      showStatus('禁用代理失败: ' + (response?.message || '未知错误'), 'error');
    }
  } catch (error) {
    showStatus('禁用代理异常: ' + error.message, 'error');
  }
  
  disableBtn.disabled = false;
  disableBtn.textContent = '🚫 禁用代理';
}

// 从存储中加载凭据
function loadCredentials() {
  chrome.storage.local.get(['dataimpulse_username', 'dataimpulse_password'], (result) => {
    if (result.dataimpulse_username) {
      usernameInput.value = result.dataimpulse_username;
    }
    if (result.dataimpulse_password) {
      passwordInput.value = result.dataimpulse_password;
    }
  });
}

// 保存凭据到存储
function saveCredentials() {
  const username = usernameInput.value.trim();
  const password = passwordInput.value.trim();
  
  if (username && password) {
    chrome.storage.local.set({
      dataimpulse_username: username,
      dataimpulse_password: password
    });
  }
}

// 事件监听器
testIPBtn.addEventListener('click', testIP);
proxy1Btn.addEventListener('click', () => switchToProxy('proxy1'));
proxy2Btn.addEventListener('click', () => switchToProxy('proxy2'));
disableBtn.addEventListener('click', disableProxy);

// 保存凭据
usernameInput.addEventListener('blur', saveCredentials);
passwordInput.addEventListener('blur', saveCredentials);

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
  console.log('🎯 DataImpulse Popup初始化完成');
  
  // 加载保存的凭据
  loadCredentials();
  
  // 自动测试当前IP
  testIP();
  
  showStatus('DataImpulse代理扩展已就绪', 'success');
});

// 键盘快捷键
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey) {
    switch(e.key) {
      case '1':
        e.preventDefault();
        switchToProxy('proxy1');
        break;
      case '2':
        e.preventDefault();
        switchToProxy('proxy2');
        break;
      case 'd':
        e.preventDefault();
        disableProxy();
        break;
      case 't':
        e.preventDefault();
        testIP();
        break;
    }
  }
});

console.log('🚀 DataImpulse Popup脚本加载完成');
