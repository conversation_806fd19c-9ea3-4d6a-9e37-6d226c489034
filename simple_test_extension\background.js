console.log('Simple test extension loaded');

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Received message:', message);
  
  if (message.type === 'ping') {
    sendResponse({
      success: true,
      message: 'Simple extension is working',
      extensionId: chrome.runtime.id
    });
  }
  
  return true;
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Simple test extension installed');
});
