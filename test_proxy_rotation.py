"""
代理轮换测试脚本
测试目标：
1. 验证插件挂代理功能
2. 验证程序控制插件轮换代理
3. 验证代理切换的稳定性和速度
"""

import time
from proxy_controller import ProxyController


def test_proxy_rotation():
    """测试代理轮换功能"""
    print("🔄 代理轮换测试")
    print("=" * 50)
    
    # 测试用的代理配置（可以替换为任何代理服务器）
    PROXY_CONFIGS = [
        {
            'id': 'proxy_1',
            'host': 'gw.dataimpulse.com',
            'port': 19965,
            'name': 'Test Proxy 1',
            'username': '0465846b31e1f583b17c__cr.us',
            'password': '16af34bf75f0573a'
        },
        {
            'id': 'proxy_2', 
            'host': 'gw.dataimpulse.com',
            'port': 19966,
            'name': 'Test Proxy 2',
            'username': '0465846b31e1f583b17c__cr.us',
            'password': '16af34bf75f0573a'
        }
    ]
    
    print(f"📋 测试配置:")
    for config in PROXY_CONFIGS:
        print(f"   {config['id']}: {config['host']}:{config['port']}")
    print()
    
    try:
        # 初始化代理控制器
        print("🚀 初始化代理控制器...")
        controller = ProxyController("rotation_test")
        
        print("\n" + "="*50)
        print("步骤1: 配置代理服务器")
        print("="*50)
        
        # 动态添加代理配置
        for config in PROXY_CONFIGS:
            success = controller.add_proxy_config(
                config_id=config['id'],
                host=config['host'],
                port=config['port'],
                name=config['name']
            )
            if not success:
                print(f"❌ 配置 {config['id']} 添加失败")
                return False
        
        print("✅ 所有代理配置添加成功")
        
        print("\n" + "="*50)
        print("步骤2: 测试代理轮换")
        print("="*50)
        
        # 记录IP变化
        ip_history = []
        
        # 测试初始IP（无代理）
        print("\n🌐 测试初始IP（无代理）:")
        initial_ip = controller.test_ip()
        ip_history.append(('初始', initial_ip))
        
        # 轮换测试
        for i, config in enumerate(PROXY_CONFIGS, 1):
            print(f"\n⚡ 轮换 {i}: 切换到 {config['name']}")
            print("-" * 30)
            
            # 切换代理
            start_time = time.time()
            success = controller.switch_proxy(
                config['id'], 
                config['username'], 
                config['password']
            )
            switch_time = time.time() - start_time
            
            if success:
                print(f"✅ 代理切换成功，耗时: {switch_time:.2f}秒")
                
                # 等待代理生效
                print("⏳ 等待代理生效...")
                time.sleep(3)
                
                # 测试新IP
                new_ip = controller.test_ip()
                ip_history.append((config['name'], new_ip))
                
                # 验证IP是否变化
                if new_ip != initial_ip:
                    print(f"🎉 IP成功变更: {initial_ip} → {new_ip}")
                else:
                    print("⚠️ IP未变更，可能代理未生效")
                
                # 访问测试网站
                print("🌐 访问测试网站...")
                try:
                    controller.get('https://api.ipify.org/', timeout=10)
                    print("✅ 网站访问成功")
                except Exception as e:
                    print(f"❌ 网站访问失败: {e}")
                    
            else:
                print(f"❌ 代理切换失败")
                ip_history.append((config['name'], "切换失败"))
        
        print("\n" + "="*50)
        print("步骤3: 恢复直连")
        print("="*50)
        
        # 禁用代理，恢复直连
        print("\n🚫 禁用代理，恢复直连:")
        if controller.disable_proxy():
            print("✅ 代理禁用成功")
            time.sleep(3)
            final_ip = controller.test_ip()
            ip_history.append(('恢复直连', final_ip))
            print(f"🔄 IP恢复: {ip_history[-2][1]} → {final_ip}")
        else:
            print("❌ 禁用代理失败")
        
        print("\n" + "="*50)
        print("🎯 测试总结")
        print("="*50)
        
        print("\n📊 IP变化历史:")
        for i, (name, ip) in enumerate(ip_history, 1):
            print(f"   {i}. {name}: {ip}")
        
        # 分析结果
        unique_ips = set(ip for _, ip in ip_history if ip != "切换失败")
        print(f"\n📈 统计信息:")
        print(f"   总切换次数: {len(PROXY_CONFIGS)}")
        print(f"   成功切换次数: {len([ip for _, ip in ip_history if ip != '切换失败']) - 2}")  # 减去初始和最终
        print(f"   唯一IP数量: {len(unique_ips)}")
        print(f"   代理轮换成功: {'✅' if len(unique_ips) >= 3 else '❌'}")  # 至少3个不同IP（初始+2个代理）
        
        print("\n✨ 代理轮换测试完成！")
        
        return True
        
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        print("\n🔧 可能的原因:")
        print("1. 扩展文件夹不存在: universal_proxy_extension/")
        print("2. Chrome浏览器问题")
        print("3. 代理服务器连接问题")
        print("4. 网络连接问题")
        return False
        
    finally:
        try:
            controller.close()
            print("\n🔒 代理控制器已关闭")
        except:
            pass


def test_extension_functionality():
    """测试扩展基础功能"""
    print("\n" + "="*50)
    print("🔧 扩展功能测试")
    print("="*50)
    
    try:
        controller = ProxyController("extension_test")
        
        # 测试扩展连接
        print("📡 测试扩展连接...")
        if controller.extension_id:
            print(f"✅ 扩展连接成功，ID: {controller.extension_id}")
        else:
            print("❌ 扩展连接失败")
            return False
        
        # 测试IP检测
        print("\n🌐 测试IP检测功能...")
        ip = controller.test_ip()
        if ip:
            print(f"✅ IP检测成功: {ip}")
        else:
            print("❌ IP检测失败")
            return False
        
        print("\n✅ 扩展功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 扩展功能测试失败: {e}")
        return False
    finally:
        try:
            controller.close()
        except:
            pass


def main():
    """主测试函数"""
    print("🚀 代理插件控制系统测试")
    print("=" * 60)
    print("测试目标:")
    print("1. ✅ 验证插件挂代理功能")
    print("2. ✅ 验证程序控制插件轮换代理")
    print("3. ✅ 验证代理切换稳定性")
    print("=" * 60)
    
    # 测试1: 扩展基础功能
    if not test_extension_functionality():
        print("\n❌ 扩展基础功能测试失败，停止后续测试")
        return
    
    # 测试2: 代理轮换功能
    if test_proxy_rotation():
        print("\n🎉 所有测试通过！")
        print("\n💡 系统已验证:")
        print("✅ 插件可以正常挂代理")
        print("✅ 程序可以控制插件轮换代理")
        print("✅ 代理切换速度快且稳定")
        print("✅ 支持动态配置，无硬编码")
    else:
        print("\n❌ 代理轮换测试失败")


if __name__ == "__main__":
    main()
