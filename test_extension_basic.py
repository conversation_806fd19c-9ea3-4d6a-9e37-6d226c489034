"""
基础扩展测试 - 验证扩展是否正确加载
"""

import time
import os
from DrissionPage import ChromiumPage, ChromiumOptions


def test_extension_loading():
    """测试扩展是否正确加载"""
    print("🔧 基础扩展加载测试")
    print("=" * 40)
    
    # 扩展路径 - 先测试简单扩展
    extension_path = os.path.join(os.getcwd(), 'simple_test_extension')
    
    if not os.path.exists(extension_path):
        print(f"❌ 扩展文件夹不存在: {extension_path}")
        return False
    
    print(f"📁 扩展路径: {extension_path}")
    
    try:
        # 配置浏览器
        print("🚀 启动浏览器...")
        co = ChromiumOptions()

        # 尝试不同的扩展加载方法
        try:
            co.add_extension(extension_path)
            print("✅ 使用add_extension方法")
        except:
            try:
                co.set_argument(f'--load-extension={extension_path}')
                print("✅ 使用--load-extension参数")
            except:
                print("❌ 扩展加载方法都失败了")
                return False

        co.set_argument('--disable-web-security')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--no-sandbox')
        co.set_argument('--disable-extensions-except=' + extension_path)
        
        page = ChromiumPage(co)
        print("✅ 浏览器启动成功")
        
        # 等待扩展加载
        print("⏳ 等待扩展加载...")
        time.sleep(3)

        # 检查扩展是否可用
        print("🔍 检查扩展可用性...")

        # 访问扩展管理页面来检查扩展是否加载
        print("📋 访问扩展管理页面...")
        page.get('chrome://extensions/')
        time.sleep(2)

        # 检查页面内容
        page_content = page.html
        if 'Simple Test Extension' in page_content:
            print("✅ 扩展已在管理页面中找到")
        else:
            print("❌ 扩展未在管理页面中找到")
            # 检查是否有任何扩展
            if 'extension' in page_content.lower() or 'chrome-extension' in page_content:
                print("🔍 页面中发现扩展相关内容")
            else:
                print("🔍 页面中未发现任何扩展内容")
            return False
        
        # 方法2: 获取所有扩展
        try:
            extensions_info = page.run_js("""
                new Promise((resolve) => {
                    chrome.management.getAll((extensions) => {
                        resolve(extensions.filter(ext => ext.type === 'extension'));
                    });
                })
            """)
            
            print(f"📦 已安装的扩展数量: {len(extensions_info)}")
            for ext in extensions_info:
                print(f"   - {ext.get('name', 'Unknown')}: {ext.get('id', 'Unknown ID')}")
                if 'Universal Proxy' in ext.get('name', ''):
                    print(f"✅ 找到目标扩展: {ext['id']}")
                    
        except Exception as e:
            print(f"⚠️ 无法获取扩展列表: {e}")
        
        # 方法3: 尝试直接ping扩展
        print("\n🏓 尝试ping扩展...")
        
        # 先尝试获取当前页面的扩展上下文
        try:
            current_id = page.run_js("chrome.runtime.id")
            print(f"当前页面扩展ID: {current_id}")
        except:
            print("当前页面不在扩展上下文中")
        
        # 尝试向扩展发送消息
        try:
            # 由于我们不知道确切的扩展ID，尝试几种可能的方式
            test_ids = [
                "universal-proxy-extension",
                "dataimpulse-extension", 
                None  # 发送给自己
            ]
            
            for test_id in test_ids:
                print(f"\n🔄 测试扩展ID: {test_id}")
                try:
                    if test_id is None:
                        # 发送给自己（如果当前在扩展上下文中）
                        result = page.run_js("""
                            new Promise((resolve, reject) => {
                                const timeout = setTimeout(() => reject('timeout'), 3000);
                                chrome.runtime.sendMessage(
                                    { type: 'ping' },
                                    (response) => {
                                        clearTimeout(timeout);
                                        if (chrome.runtime.lastError) {
                                            reject(chrome.runtime.lastError.message);
                                        } else {
                                            resolve(response);
                                        }
                                    }
                                );
                            })
                        """)
                    else:
                        # 发送给指定扩展
                        result = page.run_js(f"""
                            new Promise((resolve, reject) => {{
                                const timeout = setTimeout(() => reject('timeout'), 3000);
                                chrome.runtime.sendMessage(
                                    '{test_id}',
                                    {{ type: 'ping' }},
                                    (response) => {{
                                        clearTimeout(timeout);
                                        if (chrome.runtime.lastError) {{
                                            reject(chrome.runtime.lastError.message);
                                        }} else {{
                                            resolve(response);
                                        }}
                                    }}
                                );
                            }})
                        """)
                    
                    if result:
                        print(f"✅ 扩展响应成功: {result}")
                        print(f"🎯 正确的扩展ID: {test_id}")
                        return True
                    else:
                        print(f"❌ 扩展无响应")
                        
                except Exception as e:
                    print(f"❌ 扩展通信失败: {e}")
            
            print("❌ 所有扩展ID测试都失败了")
            return False
            
        except Exception as e:
            print(f"❌ 扩展通信测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        try:
            page.close()
            print("\n🔒 浏览器已关闭")
        except:
            pass


if __name__ == "__main__":
    success = test_extension_loading()
    if success:
        print("\n🎉 扩展加载测试成功！")
    else:
        print("\n💥 扩展加载测试失败！")
        print("\n🔧 可能的解决方案:")
        print("1. 检查扩展文件夹是否完整")
        print("2. 手动在Chrome中加载扩展")
        print("3. 检查manifest.json语法")
        print("4. 查看Chrome扩展管理页面的错误信息")
