"""
优化后的 Cloudflare 绕过测试脚本
使用 Chromium 浏览器以获得更好的稳定性
支持代理上下游逻辑测试
"""

import time
import os
import platform
import requests
from CloudflareBypasser import CloudflareBypasser
from DrissionPage import ChromiumPage, ChromiumOptions
from proxy_manager import start_proxy_with_auth, stop_proxy

from utils import logging, LOG_LANG

def get_chromium_options(browser_path: str = None, arguments=None, user_agent: str = None, proxy: str = None) -> ChromiumOptions:
    """配置并返回 Chromium 选项"""
    options = ChromiumOptions().auto_port()

    options.ignore_certificate_errors(True)
    options.set_paths(browser_path=browser_path)
    options.set_argument("-accept-lang=en-US")

    # 添加扩展
    options.add_extension("turnstilePatch")
    options.add_extension("cloudflare_ua_patch")
    options.headless(False)
    
    # Linux 系统配置
    if platform.system() == "Linux":
        options.set_argument("--no-sandbox")
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--disable-software-rasterizer')
    
    if user_agent:
        options.set_user_agent(user_agent)
    else:
        options.set_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3")

    # 设置代理
    if proxy:
        options.set_proxy(proxy)
        # 仅在使用代理时添加最小化的SSL处理参数
        options.set_argument("--ignore-certificate-errors-spki-list")
        options.set_argument("--ignore-ssl-errors-list")
        logging.info(f"[PROXY] 设置代理: {proxy}" if LOG_LANG == "zh" else f"[PROXY] Setting proxy: {proxy}")

    for argument in arguments:
        options.set_argument(argument)
    return options


def test_ip_without_proxy():
    """测试不使用代理时的IP地址"""
    try:
        response = requests.get("https://api.ipify.org/", timeout=10)
        if response.status_code == 200:
            ip = response.text.strip()
            logging.info(f"[IP] 直连IP地址: {ip}" if LOG_LANG == "zh" else f"[IP] Direct IP address: {ip}")
            return ip
        else:
            logging.error(f"[ERROR] 获取直连IP失败，状态码: {response.status_code}" if LOG_LANG == "zh" else f"[ERROR] Failed to get direct IP, status code: {response.status_code}")
            return None
    except Exception as e:
        logging.error(f"[ERROR] 获取直连IP异常: {str(e)}" if LOG_LANG == "zh" else f"[ERROR] Direct IP request exception: {str(e)}")
        return None

def test_ip_with_proxy(proxy_address):
    """测试使用代理时的IP地址"""
    if not proxy_address:
        return None

    # 构建代理配置
    proxies = {
        'http': proxy_address,
        'https': proxy_address
    }

    logging.info(f"[DEBUG] 使用代理配置: {proxies}" if LOG_LANG == "zh" else f"[DEBUG] Using proxy config: {proxies}")

    # 尝试多个IP检测服务
    ip_services = [
        "https://api.ipify.org/",
        "http://httpbin.org/ip",
        "https://ifconfig.me/ip",
        "http://icanhazip.com/"
    ]

    for service in ip_services:
        try:
            logging.info(f"[DEBUG] 尝试服务: {service}" if LOG_LANG == "zh" else f"[DEBUG] Trying service: {service}")

            response = requests.get(service, proxies=proxies, timeout=20)
            if response.status_code == 200:
                if "httpbin.org" in service:
                    # httpbin返回JSON格式
                    import json
                    ip = json.loads(response.text)['origin'].split(',')[0].strip()
                else:
                    ip = response.text.strip()

                logging.info(f"[IP] 代理IP地址: {ip} (来源: {service})" if LOG_LANG == "zh" else f"[IP] Proxy IP address: {ip} (source: {service})")
                return ip
            else:
                logging.warning(f"[WARNING] 服务 {service} 返回状态码: {response.status_code}" if LOG_LANG == "zh" else f"[WARNING] Service {service} returned status code: {response.status_code}")

        except requests.exceptions.Timeout:
            logging.warning(f"[WARNING] 服务 {service} 超时" if LOG_LANG == "zh" else f"[WARNING] Service {service} timed out")
            continue
        except requests.exceptions.ConnectionError as e:
            logging.warning(f"[WARNING] 服务 {service} 连接错误: {str(e)}" if LOG_LANG == "zh" else f"[WARNING] Service {service} connection error: {str(e)}")
            continue
        except Exception as e:
            logging.warning(f"[WARNING] 服务 {service} 异常: {str(e)}" if LOG_LANG == "zh" else f"[WARNING] Service {service} exception: {str(e)}")
            continue

    logging.error("[ERROR] 所有IP检测服务都失败了" if LOG_LANG == "zh" else "[ERROR] All IP detection services failed")
    return None

def test_proxy_functionality():
    """测试代理上下游功能"""
    # 代理配置 - 使用主人的代理
    auth_proxy = "socks5h://0465846b31e1f583b17c__cr.us:<EMAIL>:10100"

    logging.info("[PROXY] 开始测试代理上下游逻辑..." if LOG_LANG == "zh" else "[PROXY] Starting proxy upstream/downstream logic test...")

    # 先测试直连IP
    direct_ip = test_ip_without_proxy()

    no_auth_proxy = None
    try:
        # 启动代理转换
        no_auth_proxy = start_proxy_with_auth(auth_proxy)
        logging.info(f"[SUCCESS] 代理转换成功: {no_auth_proxy}" if LOG_LANG == "zh" else f"[SUCCESS] Proxy conversion successful: {no_auth_proxy}")

        # 等待代理启动
        logging.info("[DEBUG] 等待代理服务稳定..." if LOG_LANG == "zh" else "[DEBUG] Waiting for proxy service to stabilize...")
        time.sleep(5)  # 增加等待时间

        # 测试代理连通性
        logging.info("[DEBUG] 开始测试代理IP..." if LOG_LANG == "zh" else "[DEBUG] Starting proxy IP test...")
        proxy_ip = test_ip_with_proxy(no_auth_proxy)

        # 比较IP地址
        if direct_ip and proxy_ip:
            if direct_ip != proxy_ip:
                logging.info(f"[SUCCESS] IP切换成功: {direct_ip} -> {proxy_ip}" if LOG_LANG == "zh" else f"[SUCCESS] IP switch successful: {direct_ip} -> {proxy_ip}")
            else:
                logging.warning(f"[WARNING] IP未切换: {direct_ip} == {proxy_ip}" if LOG_LANG == "zh" else f"[WARNING] IP not switched: {direct_ip} == {proxy_ip}")

        return no_auth_proxy

    except Exception as e:
        logging.error(f"[ERROR] 代理启动失败: {str(e)}" if LOG_LANG == "zh" else f"[ERROR] Proxy startup failed: {str(e)}")
        return None

def test_proxy_functionality_with_new_proxy():
    """使用新的SOCKS5代理进行测试"""
    # 新的代理配置
    auth_proxy = "socks5://aabczpkn:tzy0nn7slkhy@**************:6641"

    logging.info("[PROXY] 开始测试新的SOCKS5代理..." if LOG_LANG == "zh" else "[PROXY] Starting new SOCKS5 proxy test...")

    # 先测试直连IP
    direct_ip = test_ip_without_proxy()

    no_auth_proxy = None
    try:
        # 启动新代理转换
        no_auth_proxy = start_proxy_with_auth(auth_proxy)
        logging.info(f"[SUCCESS] 新代理转换成功: {no_auth_proxy}" if LOG_LANG == "zh" else f"[SUCCESS] New proxy conversion successful: {no_auth_proxy}")

        # 等待代理启动
        logging.info("[DEBUG] 等待新代理服务稳定..." if LOG_LANG == "zh" else "[DEBUG] Waiting for new proxy service to stabilize...")
        time.sleep(5)  # 增加等待时间

        # 测试代理连通性
        logging.info("[DEBUG] 开始测试新代理IP..." if LOG_LANG == "zh" else "[DEBUG] Starting new proxy IP test...")
        proxy_ip = test_ip_with_proxy(no_auth_proxy)

        # 比较IP地址
        if direct_ip and proxy_ip:
            if direct_ip != proxy_ip:
                logging.info(f"[SUCCESS] 新代理IP切换成功: {direct_ip} -> {proxy_ip}" if LOG_LANG == "zh" else f"[SUCCESS] New proxy IP switch successful: {direct_ip} -> {proxy_ip}")
            else:
                logging.warning(f"[WARNING] 新代理IP未切换: {direct_ip} == {proxy_ip}" if LOG_LANG == "zh" else f"[WARNING] New proxy IP not switched: {direct_ip} == {proxy_ip}")

        return no_auth_proxy

    except Exception as e:
        logging.error(f"[ERROR] 新代理启动失败: {str(e)}" if LOG_LANG == "zh" else f"[ERROR] New proxy startup failed: {str(e)}")
        return None

def main():
    # 使用 Chromium 路径（可通过 CHROME_PATH 环境变量自定义）
    browser_path = os.getenv('CHROME_PATH', r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")

    if not os.path.exists(browser_path):
        if LOG_LANG == "zh":
            logging.error(f"浏览器路径不存在: {browser_path}")
        else:
            logging.error(f"Browser path does not exist: {browser_path}")
        return

    arguments = [
        "-no-first-run",
        "-force-color-profile=srgb",
        "-metrics-recording-only",
        "-password-store=basic",
        "-use-mock-keychain",
        "-export-tagged-pdf",
        "-no-default-browser-check",
        "-disable-background-mode",
        "-enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions",
        "-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",
        "-deny-permission-prompts",
        "-accept-lang=en-US",
        "--lang=en-US",
        '--accept-languages=en-US,en',
        "--window-size=1024,768",
    ]

    # 基础功能已确认正常，现在测试代理版本
    logging.info("[TEST] 基础功能正常，现在测试新代理..." if LOG_LANG == "zh" else "[TEST] Basic functionality confirmed, now testing new proxy...")
    proxy_address = test_proxy_functionality_with_new_proxy()

    custom_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    options = get_chromium_options(browser_path, arguments, user_agent=custom_ua, proxy=proxy_address)
    options.set_retry(20, 0.5)

    driver = None
    try:
        # 创建浏览器实例
        driver = ChromiumPage(addr_or_opts=options)

        if LOG_LANG == "zh":
            logging.info('正在访问测试页面...')
        else:
            logging.info('Navigating to test page...')
        driver.get('https://nopecha.com/demo/cloudflare')
        # driver.get('https://x.com/account/access')

        if LOG_LANG == "zh":
            logging.info('开始绕过Cloudflare验证...')
        else:
            logging.info('Starting Cloudflare bypass...')
        cf_bypasser = CloudflareBypasser(driver)
        cf_bypasser.bypass()

        if LOG_LANG == "zh":
            logging.info("验证成功，可以访问内容!")
            logging.info("页面标题: %s", driver.title)
        else:
            logging.info("Verification successful, content accessible!")
            logging.info("Page title: %s", driver.title)

        time.sleep(5)
    except Exception as e:
        if LOG_LANG == "zh":
            logging.error("发生错误: %s", str(e))
        else:
            logging.error("Error occurred: %s", str(e))
    finally:
        # 清理资源
        if driver:
            if LOG_LANG == "zh":
                logging.info('正在关闭浏览器...')
            else:
                logging.info('Closing browser...')
            driver.quit()

        # 停止代理服务
        if proxy_address:
            try:
                stop_proxy(proxy_address)
                logging.info("[PROXY] 代理服务已停止" if LOG_LANG == "zh" else "[PROXY] Proxy service stopped")
            except Exception as e:
                logging.error(f"[ERROR] 停止代理服务失败: {str(e)}" if LOG_LANG == "zh" else f"[ERROR] Failed to stop proxy service: {str(e)}")


if __name__ == '__main__':
    main()
