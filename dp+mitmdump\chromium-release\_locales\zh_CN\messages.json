{"appNameShort": {"message": "ZeroOmega"}, "manifest_app_name": {"message": "ZeroOmega"}, "manifest_app_description": {"message": "轻松快捷地管理和切换多个代理设置。"}, "manifest_icon_default_title": {"message": "正在加载……"}, "upgrade_profile_auto": {"message": "自动切换"}, "profile_direct": {"message": "[直接连接]"}, "profile_direct_badge_text": {"message": "直连"}, "profile_system": {"message": "[系统代理]"}, "profile_system_badge_text": {"message": "系统"}, "condition_HostWildcardCondition": {"message": "域名通配符"}, "condition_help_HostWildcardCondition": {"message": "根据域名（主机名）匹配请求。<br><b>星号 <code>*</code></b> 匹配零个或者多个字符。<br><b>问号 <code>?</code></b> 匹配任意一个字符。<br><br>请注意以 <code>*.</code> 开头的规则有特别处理，会同时匹配子域名和自身。<br>例如: <code>*.example.com</code> 能匹配 www.example.com ，<b>而且也能匹配 example.com 。</b><br>如果<b>只需要匹配子域名</b>，请使用<b>两个</b>星号开头，如 <code>**.example.com</code>。"}, "condition_HostRegexCondition": {"message": "域名正则"}, "condition_help_HostRegexCondition": {"message": "类似主机通配符条件，但通过<a href='https://www.google.com/search?q=regular%20expression'>正则表达式</a>匹配主机。<br>正则表达式很难编写，且可读性差。<br>因此，多数情况下建议使用通配符，只在其他任何条件类型都不能达到的情况下使用正则表达式。"}, "condition_HostLevelsCondition": {"message": "域名层数"}, "condition_help_HostLevelsCondition": {"message": "如果域名层数在设定的范围内则匹配，否则不匹配。<br>域名层数是指 <b>域名共有几段（以点分隔）</b>.<br>例如: <code>www.example.com</code> 的域名层数为 3，而 <code>internal</code> 的域名层数为 1."}, "condition_IpCondition": {"message": "IP 地址字面量"}, "condition_help_IpCondition": {"message": "当且仅当主机是<b>字面的</b> IP 地址，且地址处于某个子网内时匹配。子网使用 <a href='http://www.ibm.com/support/knowledgecenter/zh/SS2MBL_9.0.2/PCAAdmin/PCA/Cfg/CIDRFormat_47.html'>CIDR 格式</a>表示。<br>例如，规则 <code>127.0.0.1/16</code> 会匹配所有类似 <code>127.0.*.*</code> 的地址。<br>因此地址 <code>127.0.0.1</code> 匹配而地址 <code>*********</code> 不匹配。主机名称，例如 <code>localhost</code> 不会被此类规则匹配，因为它们<b>不是</b> IP 地址字面量。"}, "condition_UrlWildcardCondition": {"message": "网址通配符"}, "condition_help_UrlWildcardCondition": {"message": "根据通配符规则匹配网址。<br>关于通配符表达式，请参考上方的域名通配符一节的说明。<br>请注意网址通配符没有任何特殊处理，不会特殊处理子域名等。<br>所以 <code>*://*.example.com/*</code> 能匹配 http://www.example.com/ 但是 <b>不匹配</b> http://example.com/."}, "condition_UrlRegexCondition": {"message": "网址正则"}, "condition_help_UrlRegexCondition": {"message": "使用功能强大的<a href='https://www.google.com/search?q=regular%20expression'>regular expression</a>正则表达式</a>来匹配URL。<br>但正则表达式很难编写，且可读性差。<br>因此，建议在大多数情况下使用通配符，只在任何其他条件类型都无法实现的情况下使用正则表达式。"}, "condition_KeywordCondition": {"message": "关键字"}, "condition_help_KeywordCondition": {"message": "关键字条件的具体匹配规则是：网址协议为HTTP且网址中包含该关键字。<br>类似于 <code>http://*<b>关键字</b>*</code>, 其中 <b>关键字</b> 是设定好的关键字。<br>如果某防火墙根据网址中是否包含关键字来屏蔽网址，那么可以使用关键字条件来通过代理访问这样的请求，以达到绕过防火墙的目的。"}, "condition_FalseCondition": {"message": "(禁用)"}, "condition_details_FalseCondition": {"message": "(匹配请求时无视此条规则)"}, "condition_help_FalseCondition": {"message": "设置规则类型为<code>(禁用)</code>可以临时禁用某个条件。禁用的条件在匹配时视为不存在。<br>条件被禁用后，仍然保存有之前的数据（例如通配符或正则），因此当需要时，可以把条件类型改回之前的类型，以方便地重新启用条件。"}, "condition_TimeCondition": {"message": "当前时间"}, "condition_help_TimeCondition": {"message": "如果当前本地时间在某个范围内则匹配。此范围由<b>开始小时</b>和<b>结束小时</b>确定，包含开始的那个小时以及结束的那个小时。<br>本地时间、开始小时和结束小时均按照<b>24小时制</b>计算（从<b>0到23</b>）。<br>此条件大约在请求发出的瞬间，才计算是否匹配。"}, "condition_WeekdayCondition": {"message": "每周几"}, "condition_help_WeekdayCondition": {"message": "只在每周的某几天才匹配。可以在条件详情中勾选<b>星期几有效</b>。根据当地时区来计算现在是星期几，然后再查看当天是否选中。<br>假设在请求发送时是星期X：如果星期X被勾选，则匹配所有请求。否则不匹配任何请求。<br>除了日期以外，在匹配过程中不会参考请求的网址或任何其他信息。"}, "condition_alert_fullUrlLimitation": {"message": "Chrome 52 起，<code>https://</code>协议下的完整网址无法正常匹配。<a href='https://github.com/FelisCatus/SwitchyOmega/wiki/Chromium-%E5%AE%8C%E6%95%B4%E7%BD%91%E5%9D%80%E9%99%90%E5%88%B6'>更多信息...</a>"}, "condition_alert_fullUrlLimitationLink": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/Chromium-%E5%AE%8C%E6%95%B4%E7%BD%91%E5%9D%80%E9%99%90%E5%88%B6"}, "condition_group_default": {"message": ""}, "condition_group_host": {"message": "域名"}, "condition_group_url": {"message": "网址"}, "condition_group_special": {"message": "特殊"}, "ruleListFormat_Switchy": {"message": "Switchy"}, "ruleListFormat_AutoProxy": {"message": "AutoProxy"}, "ruleList_usageUrl": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/RuleListUsage"}, "ruleList_error_resultNotEnabled": {"message": "语法错误：缺少 '@with result' 指令！"}, "ruleList_error_unknownProfile": {"message": "未找到此情景模式: $PROFILE$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "ruleList_error_missingResultProfile": {"message": "语法错误：缺少结果情景模式名称。 行号 $LNO$: $SOURCE$", "placeholders": {"_unused_0": {"content": "$0"}, "LNO": {"content": "$1"}, "SOURCE": {"content": "$2"}}}, "ruleList_error_invalidRule": {"message": "语法错误：非法规则。行号 $LNO$: $SOURCE$", "placeholders": {"_unused_0": {"content": "$0"}, "LNO": {"content": "$1"}, "SOURCE": {"content": "$2"}}}, "ruleList_error_noDefaultRule": {"message": "缺少匹配全部请求的'*'默认规则！"}, "dialog_close": {"message": "关闭"}, "dialog_save": {"message": "保存更改"}, "dialog_ok": {"message": "确定"}, "dialog_cancel": {"message": "取消"}, "inputClear_clear": {"message": "清空"}, "inputClear_restore": {"message": "还原"}, "options_title": {"message": "ZeroOmega 选项"}, "options_experimental_badge": {"message": "测试版"}, "options_navHeader_setting": {"message": "设置"}, "options_navHeader_profiles": {"message": "情景模式"}, "options_navHeader_actions": {"message": "操作"}, "options_tab_ui": {"message": "界面"}, "options_tab_general": {"message": "通用"}, "options_tab_importExport": {"message": "导入/导出"}, "options_newProfile": {"message": "新建情景模式…"}, "options_apply": {"message": "应用选项"}, "options_discard": {"message": "撤销更改"}, "options_reset": {"message": "重置选项"}, "options_group_miscOptions": {"message": "其他设置"}, "options_confirmDeletion": {"message": "删除切换条件时需要确认。"}, "options_refreshOnProfileChange": {"message": "当更改情景模式时刷新当前标签。"}, "options_showInspectMenu": {"message": "右键菜单中，可检查网页元素所使用的代理。"}, "options_addConditionsToBottom": {"message": "把以弹出菜单方式创建的规则添加到列表末尾。"}, "options_showResultProfileOnActionBadgeText": {"message": "将最终使用的情景模式名称显示到徽标上。"}, "options_group_keyboardShortcut": {"message": "键盘快捷键"}, "options_menuShortcutHelp": {"message": "按下快捷键即可打开弹出菜单来切换情景模式。(默认快捷键: Alt+Shift+O)."}, "options_menuShortcutMore": {"message": "弹出菜单中的菜单项也可以用键盘进行选择。在弹出菜单中按下? (问号键，或/斜杠键) 查看帮助。"}, "options_menuShortcutConfigure": {"message": "修改快捷键"}, "options_group_switchOptions": {"message": "切换选项"}, "options_startupProfile": {"message": "初始情景模式"}, "options_startupProfile_none": {"message": "(当前情景模式)"}, "options_showConditionTypesAdvanced": {"message": "显示高级切换条件"}, "options_showConditionTypesAdvancedHelp": {"message": "解锁一些新种类的、功能强大的但难以掌握的切换条件。对于大多数情况来说，基本条件类型应该就足够，因此不推荐该选项。"}, "options_quickSwitch": {"message": "快速切换"}, "options_cycledProfiles": {"message": "循环情景模式"}, "options_cycledProfilesHelp": {"message": "点击图标或按下快捷键时，依次循环切换到以下情景模式。"}, "options_cycledProfilesTooFew": {"message": "必须至少选择2个情景模式才能进行切换。请从下方框中拖动情景模式到此框。"}, "options_notCycledProfiles": {"message": "不循环切换的情景模式 (拖动到上面的框中启用切换)"}, "options_group_proxyChanges": {"message": "代理设置变化"}, "options_revertProxyChanges": {"message": "撤消其他扩展对代理的更改。"}, "options_group_conflicts": {"message": "冲突"}, "options_conflicts_introduction": {"message": "有时其他应用也会试图控制代理设置，从而导致冲突。请注意，去广告等其他扩展也可能利用了代理设置来实现功能。此类冲突是由浏览器的工作原理引起的，所以无法避免。"}, "options_conflicts_lowerPriority": {"message": "如果 ZeroOmega 图标上显示这样的红色徽章，表示另一个应用优先级较高，因此ZeroOmega 无法控制代理设置。请尝试卸载 ZeroOmega 再重新安装，这样可能可以提高 ZeroOmega 的优先级。如果重装后您仍然看到冲突，那么请考虑移除那个导致冲突的应用。"}, "options_conflicts_higherPriority": {"message": "如果 ZeroOmega 的优先级较高，那么您可以在弹出菜单中选择 $SYSTEMPROFILE$ 来把控制权还给其他应用或系统设置。", "placeholders": {"_unused_0": {"content": "$0"}, "SYSTEMPROFILE": {"content": "$1"}}}, "options_showExternalProfile": {"message": "在弹出菜单中显示菜单项，以导入其他应用提供的代理设置。"}, "options_showExternalProfileHelp": {"message": "选择了 $SYSTEMPROFILE$ 的情况下，您可以在弹出菜单中选择 $EXTERNALPROFILE$ 来导入其他应用提供的代理设置。导入的设置将会成为一个新的情景模式，其名称由您决定。请注意导入的情景模式只是当时的一个快照，导入后不会随着原来的应用更新。", "placeholders": {"_unused_0": {"content": "$0"}, "SYSTEMPROFILE": {"content": "$1"}, "EXTERNALPROFILE": {"content": "$2"}}}, "options_group_networkRequests": {"message": "网络请求"}, "options_monitorWebRequests": {"message": "在图标上显示当前页面中由于网络原因而未加载的资源数量。"}, "options_monitorWebRequestsHelp": {"message": "启用此选项后，如有资源加载失败，则图标上会显示数字提示。<br>此时，您可以通过弹出菜单一次设置这些资源使用的情景模式，操作十分便捷。"}, "options_downloadOptions": {"message": "下载选项"}, "options_downloadOptionsHelp": {"message": "设置规则列表和PAC脚本的更新间隔。"}, "options_downloadInterval": {"message": "更新间隔"}, "options_downloadInterval_15": {"message": "15分钟"}, "options_downloadInterval_60": {"message": "1小时"}, "options_downloadInterval_180": {"message": "3小时"}, "options_downloadInterval_360": {"message": "6小时"}, "options_downloadInterval_720": {"message": "12小时"}, "options_downloadInterval_1440": {"message": "每天一次"}, "options_downloadInterval_never": {"message": "从不更新"}, "options_group_importExportProfile": {"message": "情景模式"}, "options_exportPacFile": {"message": "导出PAC文件"}, "options_exportPacFileHelp": {"message": "导出PAC（代理自动设置）文件,以便在其它浏览器使用。"}, "options_exportProfileHelp": {"message": "如需导出情景模式，请使用情景模式设置页面右上角的工具栏。"}, "options_exportLegacyRuleList": {"message": "导出规则列表时使用 Proxy Switchy!/SwitchyPlus/SwitchySharp 兼容格式。"}, "options_exportLegacyRuleListHelp": {"message": "如果您需要发布规则列表给那些软件的用户，请启用此选项。<br>建议您提醒订阅者升级到 ZeroOmega 以享受新版功能。"}, "options_group_importExportSettings": {"message": "选项"}, "options_makeBackup": {"message": "生成备份文件"}, "options_makeBackupHelp": {"message": "导出一份包括情景模式和其他所有选项的备份文件。"}, "options_restoreLocal": {"message": "从备份文件恢复"}, "options_restoreLocalHelp": {"message": "导入本地的备份文件以恢复所有选项。"}, "options_restoreOnline": {"message": "在线恢复"}, "options_restoreOnlinePlaceholder": {"message": "备份文件地址 (如：http://example.com/switchy.bak)"}, "options_restoreOnlineSubmit": {"message": "恢复"}, "options_group_syncing": {"message": "选项同步 (测试中)"}, "options_syncEnable": {"message": "启用同步"}, "options_useBuiltInSyncEnhance": {"message": "使用浏览器内置同步功能增强 Gist 同步使用体验"}, "options_useBuiltInSyncEnhanceTip": {"message": "<li>用户登录浏览器账号后就自动下载扩展，并恢复所有已同步的设置</li><li>配置项有变化时，会立即同步到其他浏览器下．（没有打开该功能时，应用只会每隔 5 分钟检查配置是否变化）</li>"}, "options_syncEnableForce": {"message": "下载云端版本"}, "options_syncDisable": {"message": "禁用同步"}, "options_syncReset": {"message": "删除云端版本"}, "options_syncPristineHelp": {"message": "您可以将设置和情景模式同步到所有使用Chrome浏览器的桌面设备。"}, "options_syncSyncAlert": {"message": "您的设置将会自动与其他设备进行同步。"}, "options_syncSyncHelp": {"message": "请注意：您需要在所有设备上（包括此设备）的Chrome浏览器中登录，这样同步的选项才能正常使用。<br>可以在其他设备上查看此页面，来检查同步是否生效。"}, "options_syncConflictAlert": {"message": "您已经通过其他设备上传了一份选项用于同步。"}, "options_syncConflictHelp": {"message": "您可以将云端的选项下载到此设备使用。<br>一旦选择下载，<b>此设备上的设置和情景模式将会被覆盖</b>。"}, "options_syncUnsupportedHelp": {"message": "选项同步暂不支持您的平台或浏览器。目前只支持桌面版Chrome的浏览器的同步，请谅解。"}, "options_builtin": {"message": "应用内置"}, "options_theme": {"message": "主题"}, "options_builtinProfile": {"message": "内置情境模式"}, "options_profileTabPrefix": {"message": "情景模式： "}, "options_renameProfile": {"message": "更改名称"}, "options_deleteProfile": {"message": "删除"}, "options_profileExportRuleList": {"message": "发布规则列表"}, "options_profileExportRuleListHelp": {"message": "将切换规则导出为文本格式以便发布。"}, "options_profileExportPac": {"message": "导出PAC"}, "options_profileUnsupported": {"message": "不支持的情景模式类型： $TYPE$!", "placeholders": {"_unused_0": {"content": "$0"}, "TYPE": {"content": "$1"}}}, "options_profileUnsupportedHelp": {"message": "选项文件已经损坏，或者当前版本过低无法处理选项。"}, "options_profileEditSource": {"message": "编辑源代码"}, "options_profileEditSourceHelp": {"message": "显示源代码格式相关的帮助"}, "options_profileEditSourceHelpUrl": {"message": "https://github.com/FelisCatus/SwitchyOmega/wiki/SwitchyOmega-conditions-format#result-profile"}, "options_group_proxyServers": {"message": "代理服务器"}, "options_proxy_scheme": {"message": "网址协议"}, "options_proxy_protocol": {"message": "代理协议"}, "options_proxy_server": {"message": "代理服务器"}, "options_proxy_port": {"message": "代理端口"}, "options_proxy_auth": {"message": "代理登录"}, "options_proxy_authNotSupported": {"message": "您的浏览器不支持 $PROTOCOLDISP$ 代理认证！如有问题请联系您的浏览器支持，请勿反馈此问题给 ZeroOmega.", "placeholders": {"_unused_0": {"content": "$0"}, "PROTOCOLDISP": {"content": "$1"}}}, "options_proxy_authAllWarningPac": {"message": "警告: 用户名密码将会提供给PAC脚本返回的任何服务器，有时目标服务器会出乎您的预料。"}, "options_proxy_authAllWarningPacUrl": {"message": "在提供用户名和密码时，请先确保您可以信任以上网址提供的PAC脚本。"}, "options_proxy_authAllWarningPacScript": {"message": "在提供用户名和密码时，请先确保您可以信任以下输入的PAC脚本。"}, "options_proxy_authReferencedWarning": {"message": "此外，在其他情景模式（如自动切换）中使用此情景时，可能会导致用户名和密码被发送至其他情景模式中设置的服务器。"}, "options_scheme_default": {"message": "(默认)"}, "options_protocol_direct": {"message": "直接连接"}, "options_protocol_useDefault": {"message": "(同默认)"}, "options_proxy_single": {"message": "对于所有代理使用相同服务器。"}, "options_proxy_expand": {"message": "显示高级设置"}, "options_group_bypassList": {"message": "不代理的地址列表"}, "options_bypassListHelp": {"message": "不经过代理连接的主机列表: (每行一个主机)"}, "options_bypassListHelpLinkText": {"message": "(可使用通配符等匹配规则…)"}, "options_group_pacUrl": {"message": "PAC 网址"}, "options_pacUrlHelp": {"message": "应用将从此网址下载PAC脚本。如果网址留空，则直接使用下方的脚本内容。"}, "options_pacUrlFile": {"message": "如果您使用本地PAC文件，则该情景模式只能单独使用，无法作为自动切换的结果。这是因为浏览器不允许读取本地文件。"}, "options_pacUrlFileDisabled": {"message": "此情景模式已被引用，所以不能使用本地PAC文件。如果您真的需要使用本地文件，请另外新建一个PAC情景模式。"}, "options_group_pacScript": {"message": "PAC 脚本"}, "options_pacScriptLastUpdate": {"message": "PAC 脚本下载时间 $TIME$：", "placeholders": {"_unused_0": {"content": "$0"}, "TIME": {"content": "$1"}}}, "options_pacScriptObsolete": {"message": "修改网址后尚未下载更新，因此脚本已经过时。请使用上方的更新按钮进行下载。"}, "options_group_virtualProfile": {"message": "虚情景模式"}, "options_virtualProfileTarget": {"message": "目标"}, "options_virtualProfileTargetHelp": {"message": "应用此配置文件时，它的作用与下面选择的配置文件完全相同。"}, "options_group_virtualProfileReplace": {"message": "迁移到虚情景模式"}, "options_virtualProfileReplace": {"message": "取代目标情景模式"}, "options_virtualProfileReplaceHelp": {"message": "通过此功能可以更改现有的选项，使用此虚情景模式来取代 $PROFILE$ 。此功能会把所有和 $PROFILE$ 相关的切换规则改为使用此虚情景模式。这样一来，就可以通过此虚情景模式来控制那些切换条件对应的结果。", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$2"}}}, "options_group_ruleListConfig": {"message": "规则列表设置"}, "options_ruleListFormat": {"message": "规则列表格式"}, "options_group_ruleListResult": {"message": "规则列表结果情景模式"}, "options_ruleListMatchProfile": {"message": "匹配则使用情景模式"}, "options_ruleListDefaultProfile": {"message": "不匹配则使用情景模式"}, "options_group_ruleListUrl": {"message": "规则列表网址"}, "options_ruleListUrlHelp": {"message": "应用将从此网址下载规则列表。如果网址留空，则以下文本会被直接处理后作为规则列表使用。"}, "options_group_ruleListText": {"message": "规则列表正文"}, "options_ruleListLastUpdate": {"message": "规则列表下载时间 $TIME$：", "placeholders": {"_unused_0": {"content": "$0"}, "TIME": {"content": "$1"}}}, "options_ruleListObsolete": {"message": "修改网址后尚未下载更新，因此规则列表已经过时。请使用上方的更新按钮进行下载。"}, "options_group_switchRules": {"message": "切换规则"}, "options_sort": {"message": "排序"}, "options_conditionType": {"message": "条件类型"}, "options_showConditionTypeHelp": {"message": "显示帮助"}, "options_conditionDetails": {"message": "条件设置"}, "options_resultProfile": {"message": "情景模式"}, "options_conditionActions": {"message": "操作"}, "options_addCondition": {"message": "添加条件"}, "options_cloneRule": {"message": "克隆"}, "options_ruleNote": {"message": "备注"}, "options_switchAttachedProfileInCondition": {"message": "规则列表规则"}, "options_switchAttachedProfileInConditionDetails": {"message": "(按照规则列表匹配请求)"}, "options_switchAttachedProfileInConditionDisabled": {"message": "(规则列表已禁用！)"}, "options_switchDefaultProfile": {"message": "默认情景模式"}, "options_hostLevelsBetween": {"message": "≤ 主机层数 ≤"}, "options_hourBetween": {"message": "≤ 当前小时 ≤"}, "options_weekDayShort_0": {"message": "日"}, "options_weekDayShort_1": {"message": "一"}, "options_weekDayShort_2": {"message": "二"}, "options_weekDayShort_3": {"message": "三"}, "options_weekDayShort_4": {"message": "四"}, "options_weekDayShort_5": {"message": "五"}, "options_weekDayShort_6": {"message": "六"}, "options_group_conditionHelp": {"message": "条件类型说明"}, "options_group_attachProfile": {"message": "导入在线规则列表"}, "options_attachProfile": {"message": "添加规则列表"}, "options_attachProfileHelp": {"message": "可以添加规则列表，以便引用他人在线发布的一组规则。"}, "options_modalHeader_welcome": {"message": "欢迎使用 ZeroOmega"}, "options_welcomeNormal": {"message": "您已经成功安装了 ZeroOmega ，一个强大的代理切换工具。"}, "options_welcomeNormalGuide": {"message": "您可以通过选项页面设置需要使用的代理服务器，下面我们就来试试看吧。"}, "options_welcomeUpgrade": {"message": "您已经成功升级到 ZeroOmega. 别担心，所有设置都已经升级成功，可以继续使用。"}, "options_welcomeUpgradeGuide": {"message": "现在我们来熟悉一下新的选项页面。"}, "options_guideNext": {"message": "下一步"}, "options_guideDone": {"message": "完成"}, "options_guideSkip": {"message": "跳过教程"}, "options_modalHeader_applyOptions": {"message": "应用选项"}, "options_optionsNotSaved": {"message": "当前设置还未保存。如果您继续此操作，则刚才的所有修改都会丢失！"}, "options_applyOptionsRequired": {"message": "必须保存当前选项才能继续操作。"}, "options_applyOptionsConfirm": {"message": "是否保存并应用现在的选项？"}, "options_modalHeader_renameProfile": {"message": "重命名"}, "options_renameProfileName": {"message": "新的名称"}, "options_profileNameConflict": {"message": "已经存在相同名称的情景模式。"}, "options_profileNameReserved": {"message": "以双下划线开头的名称为系统保留，禁止使用。"}, "options_profileNameHidden": {"message": "以下划线开头的情景模式不会在弹出菜单中显示，但仍可被用作切换的结果等。"}, "options_modalHeader_replaceProfile": {"message": "替换情景模式"}, "options_replaceProfile": {"message": "替换"}, "options_replaceProfileConfirm": {"message": "您确定要使用 $ToProfile 来代替 $FromProfile$ 吗?", "placeholders": {"_unused_0": {"content": "$0"}, "FromProfile": {"content": "$1"}}}, "options_replaceProfileHelp": {"message": "如果继续操作，则和 $FromProfile$ 有关的切换规则将改为使用 $ToProfile$ 来代替。此外，启动情景模式、快速切换等设置也会做相应调整。但请注意，此操作不影响这两个情景模式本身。", "placeholders": {"_unused_0": {"content": "$0"}, "FromProfile": {"content": "$1"}, "ToProfile": {"content": "$2"}}}, "options_replaceProfileSuccess": {"message": "更改选项成功。"}, "options_modalHeader_deleteProfile": {"message": "删除情景模式"}, "options_deleteProfileConfirm": {"message": "真的要删除这个情景模式吗？"}, "options_modalHeader_cannotDeleteProfile": {"message": "情景模式无法删除"}, "options_profileReferredBy": {"message": "无法删除此配置文件，因为它被以下配置文件引用："}, "options_modifyReferringProfiles": {"message": "修改以上所有情景模式并移除对此情景模式的引用后，方可删除此情景模式。"}, "options_profileNameEmpty": {"message": "情景模式名称不能为空。"}, "popup_title": {"message": "ZeroOmega 弹出菜单"}, "options_modalHeader_deleteRule": {"message": "删除规则"}, "options_modalHeader_proxyAuth": {"message": "代理登录"}, "options_proxyAuthUsername": {"message": "用户名"}, "options_proxyAuthPassword": {"message": "密码"}, "options_proxyAuthShowPassword": {"message": "显示密码"}, "options_proxyAuthHidePassword": {"message": "隐藏密码"}, "options_proxyAuthNone": {"message": "(无密码)"}, "options_deleteRuleConfirm": {"message": "真的要删除这个规则吗?"}, "options_deleteRule": {"message": "删除"}, "options_modalHeader_resetRules": {"message": "重置全部规则"}, "options_resetRulesConfirm": {"message": "真的要设置所有规则对应的情景模式为以下情景模式吗？"}, "options_resetRules": {"message": "重置规则"}, "options_resetRules_help": {"message": "批量设置所有规则的情景模式"}, "options_modalHeader_deleteAttached": {"message": "移除规则列表"}, "options_deleteAttachedConfirm": {"message": "真的要移除当前情景模式的在线规则列表吗？"}, "options_ruleListLineCount": {"message": "共计$COUNT$行规则", "placeholders": {"_unused_0": {"content": "$0"}, "COUNT": {"content": "$1"}}}, "options_deleteAttached": {"message": "移除规则列表"}, "options_modalHeader_newProfile": {"message": "新建情景模式"}, "options_newProfileName": {"message": "情景模式名称"}, "options_profileType": {"message": "请选择情景模式的类型:"}, "options_profileTypeFixedProfile": {"message": "代理服务器"}, "options_profileDescFixedProfile": {"message": "经过代理服务器访问网站。"}, "options_profileTypePacProfile": {"message": "PAC情景模式"}, "options_profileDescPacProfile": {"message": "根据在线或本地的PAC脚本选择代理。"}, "options_profileDescMorePacProfile": {"message": "如果您没有任何PAC脚本，也没有脚本的网址，则不必使用此情景模式。不了解PAC的用户不建议自行尝试编写脚本。"}, "options_profileTypeSwitchProfile": {"message": "自动切换模式"}, "options_profileDescSwitchProfile": {"message": "在不同的条件下，例如域或模式，自动应用不同的配置文件。\n为了方便切换，您也可以导入在线发布的规则。（替换 AutoSwitch 模式 + 规则列表.）"}, "options_profileTypeRuleListProfile": {"message": "规则列表"}, "options_profileDescRuleListProfile": {"message": "使用他人发布的在线规则列表来切换情景模式。"}, "options_profileTypeVirtualProfile": {"message": "虚情景模式"}, "options_profileDescVirtualProfile": {"message": "虚情景模式可以作为某个其他情景模式使用，并可以根据需要更改对象。一般用在自动切换中，这样就可以一次性更改多个条件对应的代理。"}, "options_createProfile": {"message": "创建"}, "options_modalHeader_resetOptions": {"message": "重置选项"}, "options_resetOptionsConfirm": {"message": "真的确定要重置选项吗？如果继续，现有的所有情景模式和选项将会丢失！"}, "options_formInvalid": {"message": "请更正这个页面中的错误。"}, "options_profileNotFound": {"message": "情景模式 $PROFILE$ 不存在！选项可能已经损坏。", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "options_resetSuccess": {"message": "选项已经重置。"}, "options_saveSuccess": {"message": "保存选项成功。"}, "options_importSuccess": {"message": "导入选项成功。"}, "options_importFormatError": {"message": "备份文件格式错误！"}, "options_importDownloadError": {"message": "下载备份文件时出错！"}, "options_profileDownloadSuccess": {"message": "情景模式已经更新成功。"}, "options_profileDownloadError": {"message": "下载情景模式数据时出错！"}, "options_profileDownloadError_NetworkError": {"message": "更新时发生网络错误。"}, "options_profileDownloadError_HttpError": {"message": "更新时发生网络错误 (HTTP $STATUS$).", "placeholders": {"_unused_0": {"content": "$0"}, "STATUS": {"content": "$1"}}}, "options_profileDownloadError_HttpNotFoundError": {"message": "在远程服务器上找不到情景模式网址对应的文件。请检查网址。"}, "options_profileDownloadError_HttpServerError": {"message": "更新时远程服务器发生错误 ($STATUS$)。", "placeholders": {"_unused_0": {"content": "$0"}, "STATUS": {"content": "$1"}}}, "options_profileDownloadError_ContentTypeRejectedError": {"message": "下载的数据不符合格式！建议您在浏览器中打开情景模式网址并检查其内容。"}, "options_downloadProfileNow": {"message": "立即更新情景模式"}, "options_guide_fixedProfileStep": {"message": "<b>代理情景</b>包含了服务器地址、端口等代理的信息。<br>在 ZeroOmega 中，情景模式是代理设置的基本单元。<br>默认设置中已经建立了一个代理情景模式作为样例。试着打开它吧。"}, "options_guide_fixedServersStep": {"message": "在这里，您可以填写所需的代理服务器地址和端口。<br>ZeroOmega<b>软件本身不提供任何内置代理服务器</b>。<br>如果您不清楚应该填写什么，最好咨询下您的网络提供者，或者参考代理软件的设置说明。"}, "options_guide_autoSwitchProfileStep": {"message": "您可以通过强大的<b>自动切换模式</b>在多个代理间切换自如。<br>不过，在这个简单的教程中无法详尽介绍所有功能。<br>想要使用此功能时，可以打开这里的设置界面，来了解如何使用自动切换功能。"}, "options_guide_addMoreProfilesStep": {"message": "需要更多情景模式？你始终可以添加更多<b>代理、切换和其他情景模式</b>满足你所有的代理需要。<br>教程到此结束，享受代理吧！"}, "options_guide_conditionStep": {"message": "ZeroOmega 可以根据<b>切换条件</b>对不同的网络请求使用不同的情景模式。<br>例如<b>域名通配符</b>条件可以对某个域名下的所有网址使用特定的情景模式。"}, "options_guide_conditionTypeStep": {"message": "您可以使用各种条件类型来匹配域名或者整个网址。<br> 点击问号按钮来查看条件类型的说明。"}, "options_guide_conditionProfileStep": {"message": "对于<b>任何匹配该条件的请求</b>，ZeroOmega 会使用这个情景模式。<br>如果选择了<b>\"[直接连接]\"情景模式</b>，则匹配的请求不使用任何代理。"}, "options_guide_switchDefaultStep": {"message": "如果请求不匹配任何条件，则使用默认情景模式。<br>条件的匹配顺序总是按此页面<b>从上到下</b>。<br>您可以拖动排序图标来更改条件的顺序。"}, "options_guide_applySwitchProfileStep": {"message": "当您设置完毕后，别忘记<b>在弹出菜单中启用自动切换情景模式</b>。<br/>图标将会显示标签页切换的<b>最终结果</b>情景。<br/> <b>悬停在图标上</b>则会显示切换相关的详细说明。"}, "popup_externalProfile": {"message": "(外部情景模式)"}, "popup_externalProfileName": {"message": "保存名称"}, "popup_proxyNotControllable_app": {"message": "其他应用正在控制代理设置。请禁用或者卸载发生冲突的应用。"}, "popup_proxyNotControllable_policy": {"message": "代理设置被本地策略强制指定，无法修改。请联系系统管理员。"}, "popup_proxyNotControllable_unknown": {"message": "无法设置代理设置。请检查系统和浏览器设置。"}, "popup_proxyNotControllable_disabled": {"message": "在其他应用或扩展的要求下，ZeroOmega 已经禁用代理设置。"}, "popup_proxyNotControllable_upgrade": {"message": "代理设置现在由新版本的 ZeroOmega 控制。"}, "popup_proxyNotControllableDetails": {"message": "如果不解决以上问题，则无法使用ZeroOmega切换代理。"}, "popup_proxyNotControllableDetails_upgrade": {"message": "两个不同版本的 ZeroOmega （如稳定版和测试版）不能共存。请禁用其中之一。"}, "popup_proxyNotControllableManage": {"message": "管理扩展"}, "popup_addConditionTo": {"message": "添加条件到情景模式"}, "add_temp_condition": {"message": "添加临时条件"}, "popup_addCondition": {"message": "添加条件"}, "popup_showOptions": {"message": "选项"}, "popup_reload": {"message": "重启插件"}, "popup_reportIssues": {"message": "反馈问题"}, "popup_errorLog": {"message": "保存错误日志"}, "popup_requestErrorCount": {"message": "$COUNT$个资源未加载", "placeholders": {"_unused_0": {"content": "$0"}, "COUNT": {"content": "$1"}}}, "popup_requestErrorHeading": {"message": "加载失败的资源列表"}, "popup_requestErrorWarning": {"message": "由于网络原因，此页面部分资源加载失败。这些问题可能是由您的网络、代理服务器或网站本身引起的。"}, "popup_requestErrorWarningHelp": {"message": "这些问题并非由 ZeroOmega 自身导致，它只不过检测并报告了错误而已。"}, "popup_requestErrorAddCondition": {"message": "您可以查看以下域名，并根据实际情况确定是否对其使用代理。"}, "popup_requestErrorCannotAddCondition": {"message": "在使用自动切换情景时，才可以将这些资源添加为切换条件。"}, "popup_configureMonitorWebRequests": {"message": "设置网络检测选项"}, "options_resultProfileForSelectedDomains": {"message": "对所有选中域名使用此情景模式"}, "options_pac_profile_unsupported_moz": {"message": "由于技术限制，PAC 情景模式无法在 Mozilla Firefox 上工作！"}, "popup_issueTemplate": {"message": "\n\n\n<!-- ↑请在此行上方填写问题/建议详情，可用中文↑ -->\nZeroOmega $projectVersion$\n$userAgent$", "placeholders": {"_unused_0": {"content": "$0"}, "projectVersion": {"content": "$1"}, "userAgent": {"content": "$2"}}}, "browserAction_profileDetails_PacProfile": {"message": "(PAC 脚本)"}, "browserAction_profileDetails_SystemProfile": {"message": "(由其他扩展或系统环境控制)"}, "browserAction_profileDetails_DirectProfile": {"message": "(不使用任何代理)"}, "browserAction_profileDetails_SwitchProfile": {"message": "(根据条件切换)"}, "browserAction_profileDetails_RuleListProfile": {"message": "(根据规则列表切换)"}, "browserAction_titleNormal": {"message": "ZeroOmega:: $PROFILE$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}}}, "browserAction_titleWithResult": {"message": "ZeroOmega:: $PROFILE$\n$DETAILS$", "placeholders": {"_unused_0": {"content": "$0"}, "PROFILE": {"content": "$1"}, "_unused_2": {"content": "$2"}, "DETAILS": {"content": "$3"}}}, "browserAction_titleNewerOptions": {"message": "错误：需要新版本的ZeroOmega才能加载当前选项。"}, "browserAction_titleOptionError": {"message": "错误：选项文件已经损坏，点击此处重置选项。"}, "browserAction_titleDownloadFail": {"message": "警告：更新PAC文件或规则列表失败。"}, "browserAction_titleExternalProxy": {"message": "注意：其他应用正在控制当前代理设置。"}, "browserAction_titleInspect": {"message": "[检查] $URL$", "placeholders": {"_unused_0": {"content": "$0"}, "URL": {"content": "$1"}}}, "browserAction_defaultRuleDetails": {"message": "(默认)"}, "browserAction_directResult": {"message": "直接连接"}, "browserAction_attachedPrefix": {"message": "(列表) "}, "browserAction_tempRulePrefix": {"message": "(临时) "}, "contextMenu_inspectPage": {"message": "检查此页面使用的代理"}, "contextMenu_inspectFrame": {"message": "检查此[框架页面]使用的代理"}, "contextMenu_inspectLink": {"message": "检查此[链接目标]将会使用的代理"}, "contextMenu_inspectElement": {"message": "检查此[元素]使用的代理"}, "contextMenu_enableQuickSwitch": {"message": "启用快速切换"}, "about_title": {"message": "关于"}, "about_app_description": {"message": "一个代理设置工具"}, "about_version": {"message": "版本 $VERSION$", "placeholders": {"_unused_0": {"content": "$0"}, "VERSION": {"content": "$1"}}}, "about_experimental_warning_moz": {"message": "Mozilla Firefox 浏览器支持目前仍处于早期实验阶段！如果您遇到任何问题，请使用下方的按钮进行反馈。"}, "about_disclaimer_networkService": {"message": "ZeroOmega 不提供代理服务器、VPN等网络服务。"}, "about_disclaimer_privacy": {"message": "ZeroOmega 不会跟踪您的上网记录，不在页面中插入广告。请参见我们的<a href='https://github.com/FelisCatus/SwitchyOmega/wiki/Privacy#%E4%B8%AD%E6%96%87'>隐私政策</a>。"}, "about_help": {"message": "有其它问题？在使用 ZeroOmega 方面需要帮助？请参阅我们的<a href='https://github.com/FelisCatus/SwitchyOmega/wiki/FAQ'>常见问题</a>。"}, "about_copyright": {"message": "版权所有 2012-2017 <a href='https://github.com/FelisCatus/SwitchyOmega/blob/master/AUTHORS'>The SwitchyOmega Authors</a>. 保留所有权利。<br>版权所有 2024-2025 <a href='https://github.com/zero-peak/ZeroOmega/graphs/contributors'>The ZeroOmega Authors</a>."}, "about_credits": {"message": "ZeroOmega 的诞生离不开 <a href='https://github.com/zero-peak/ZeroOmega'>ZeroOmega</a> 开源项目和其他<a href='https://github.com/FelisCatus/SwitchyOmega/blob/master/AUTHORS'>开源软件</a>。"}, "about_license": {"message": "ZeroOmega 是<a href='https://www.gnu.org/philosophy/free-sw.zh-cn.html'>自由软件</a>，使用<a href='https://www.gnu.org/licenses/gpl.html'>GNU General Public License</a> 版本 3 及以上授权。"}}