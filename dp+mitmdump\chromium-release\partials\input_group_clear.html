
<div class="input-group">
  <input ng-model="model" ng-attr-type="{{type}}" ng-pattern="ngPattern || catchAll" placeholder="{{placeholder}}" ng-change="modelChange()" class="form-control"/><span class="input-group-btn">
    <button type="button" ng-click="toggleClear()" ng-disabled="!model &amp;&amp; !oldModel" title="{{'inputClear_' + (oldModel ? 'restore' : 'clear') | tr}}" class="btn btn-default input-group-clear-btn"><span ng-class="{&quot;glyphicon-remove&quot;: !oldModel, &quot;glyphicon-repeat&quot;: !!oldModel}" class="glyphicon"></span></button></span>
</div>