#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SwitchyOmega简化代理管理方案
解决弹窗、重复页面、动态切换问题
"""

import os
import time
from DrissionPage import ChromiumPage, ChromiumOptions


class SwitchyOmegaSimple:
    """SwitchyOmega简化代理管理器"""
    
    def __init__(self):
        self.page = None
        
        # 获取扩展路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        
        self.switchyomega_path = os.path.join(script_dir, "chromium-release")
        self.ua_patch_path = os.path.join(project_root, "cloudflare_ua_patch")
        
        # SwitchyOmega扩展ID (使用正确的ID)
        self.extension_id = "padekgcemlokbadohgkifijomclgjgif"
        
        # 代理配置列表（支持动态切换）
        self.proxy_configs = [
            {
                "name": "DataImpulse1",
                "host": "gw.dataimpulse.com",
                "port": 19965,
                "username": "0465846b31e1f583b17c__cr.us",
                "password": "16af34bf75f0573a"
            },
            {
                "name": "DataImpulse2", 
                "host": "gw.dataimpulse.com",
                "port": 19966,
                "username": "0465846b31e1f583b17c__cr.us",
                "password": "16af34bf75f0573a"
            }
            # 可以添加更多代理配置
        ]
        
        self.current_proxy_index = 0
    
    def create_browser(self):
        """创建浏览器实例（基于dpMitmdumpTest.py配置）"""
        print("🚀 创建浏览器实例...")
        
        options = ChromiumOptions()
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")
        
        # 启动参数（与dpMitmdumpTest.py完全一致）
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768'
        ]
        
        # 应用所有参数
        [options.set_argument(arg) for arg in args]
        
        # 添加扩展
        if os.path.exists(self.ua_patch_path):
            print(f"✅ 加载cloudflare_ua_patch扩展: {self.ua_patch_path}")
            options.add_extension(self.ua_patch_path)
        
        if os.path.exists(self.switchyomega_path):
            print(f"✅ 加载SwitchyOmega扩展: {self.switchyomega_path}")
            options.add_extension(self.switchyomega_path)
        else:
            print("❌ SwitchyOmega扩展未找到")
            return False
        
        options.auto_port()
        
        try:
            self.page = ChromiumPage(options)
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def handle_welcome_popup(self):
        """处理ZeroOmega欢迎弹窗"""
        try:
            print("🔍 检查欢迎弹窗...")

            # 方法1: 查找"Skip guide"按钮
            skip_button = self.page.ele('x://button[contains(text(), "Skip guide")]', timeout=3)
            if skip_button:
                skip_button.click()
                time.sleep(1)
                print("✅ 已跳过欢迎向导")
                return True

            # 方法2: 查找"Next"按钮（如果用户想继续）
            next_button = self.page.ele('x://button[contains(text(), "Next")]', timeout=2)
            if next_button:
                next_button.click()
                time.sleep(1)
                print("✅ 点击了Next按钮")
                return True

            # 方法3: 查找关闭按钮（X）
            close_button = self.page.ele('x://button[@class="close"]', timeout=2)
            if close_button:
                close_button.click()
                time.sleep(1)
                print("✅ 已关闭弹窗")
                return True

            # 方法4: 点击弹窗外部区域关闭
            overlay = self.page.ele('x://div[@class="modal-backdrop"]', timeout=1)
            if overlay:
                overlay.click()
                time.sleep(1)
                print("✅ 点击外部关闭弹窗")
                return True

            print("ℹ️ 未发现弹窗或已处理")
            return True

        except Exception as e:
            print(f"⚠️ 处理弹窗时出错: {e}")
            return True  # 不影响主流程
    
    def switch_to_proxy(self, proxy_index=0):
        """切换到指定代理（基于实际界面结构）"""
        try:
            proxy_config = self.proxy_configs[proxy_index]
            print(f"\n🔄 切换到代理: {proxy_config['name']}")

            # 首先访问主选项页面
            options_url = f"chrome-extension://{self.extension_id}/options.html"
            print(f"📖 访问选项页面: {options_url}")
            self.page.get(options_url)
            time.sleep(3)

            # 处理欢迎弹窗
            self.handle_welcome_popup()

            # 查找"New profile..."按钮（基于截图显示的界面）
            new_profile_btn = self.page.ele('x://a[contains(text(), "New profile")]', timeout=5)
            if not new_profile_btn:
                # 尝试其他可能的选择器
                new_profile_btn = self.page.ele('x://button[contains(text(), "New profile")]', timeout=3)
            if not new_profile_btn:
                new_profile_btn = self.page.ele('x://*[contains(text(), "New profile")]', timeout=3)

            if new_profile_btn:
                print("✅ 找到New profile按钮")
                new_profile_btn.click()
                time.sleep(2)

                # 处理可能的弹窗
                self.handle_welcome_popup()

                # 查找代理服务器选项
                proxy_option = self.page.ele('x://button[contains(text(), "Proxy Profile")]', timeout=5)
                if not proxy_option:
                    proxy_option = self.page.ele('x://*[contains(text(), "Proxy")]', timeout=3)

                if proxy_option:
                    proxy_option.click()
                    time.sleep(2)
                    print("✅ 选择了Proxy Profile")

                    # 配置代理
                    return self.configure_proxy_in_page(proxy_config)
                else:
                    print("⚠️ 未找到Proxy Profile选项，尝试直接配置")
                    return self.configure_proxy_in_page(proxy_config)
            else:
                print("❌ 未找到New profile按钮")
                # 尝试直接访问新建页面
                new_url = f"chrome-extension://{self.extension_id}/options.html#!/profile/new"
                print(f"🔄 直接访问新建页面: {new_url}")
                self.page.get(new_url)
                time.sleep(2)
                self.handle_welcome_popup()
                return self.configure_proxy_in_page(proxy_config)

        except Exception as e:
            print(f"❌ 切换代理失败: {e}")
            return False
    
    def configure_proxy_in_page(self, proxy_config):
        """在当前页面配置代理"""
        try:
            print("🔧 配置代理参数...")
            
            # 设置配置名称
            name_input = self.page.ele('x://input[@ng-model="profile.name"]', timeout=5)
            if name_input:
                name_input.clear()
                name_input.input(proxy_config['name'])
                print(f"✅ 设置名称: {proxy_config['name']}")
            
            # 选择HTTPS协议
            https_radio = self.page.ele('x://input[@value="https"]', timeout=5)
            if https_radio:
                https_radio.click()
                time.sleep(1)
                print("✅ 选择HTTPS协议")
            
            # 配置服务器
            host_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].host"]', timeout=5)
            if host_input:
                host_input.clear()
                host_input.input(proxy_config['host'])
                print(f"✅ 设置服务器: {proxy_config['host']}")
            
            # 配置端口
            port_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].port"]', timeout=5)
            if port_input:
                port_input.clear()
                port_input.input(str(proxy_config['port']))
                print(f"✅ 设置端口: {proxy_config['port']}")
            
            # 配置认证
            auth_btn = self.page.ele('x://button[@ng-click="editProxyAuth(scheme)"]', timeout=5)
            if auth_btn:
                auth_btn.click()
                time.sleep(1)
                
                # 用户名
                username_input = self.page.ele('x://input[@ng-model="model"]', timeout=3)
                if username_input:
                    username_input.clear()
                    username_input.input(proxy_config['username'])
                    print("✅ 设置用户名")
                
                # 密码
                password_input = self.page.ele('x://input[@ng-model="auth.password"]', timeout=3)
                if password_input:
                    password_input.clear()
                    password_input.input(proxy_config['password'])
                    print("✅ 设置密码")
                
                # 保存认证
                save_auth_btn = self.page.ele('x://button[@ng-click="saveAuth()"]', timeout=3)
                if save_auth_btn:
                    save_auth_btn.click()
                    time.sleep(1)
                    print("✅ 认证已保存")
            
            # 应用配置
            apply_btn = self.page.ele('x://a[@ng-click="applyOptions()"]', timeout=5)
            if apply_btn:
                apply_btn.click()
                time.sleep(2)
                print("✅ 配置已应用")
                
                # 激活这个配置
                return self.activate_profile(proxy_config['name'])
            
            return False
            
        except Exception as e:
            print(f"❌ 配置代理失败: {e}")
            return False
    
    def activate_profile(self, profile_name):
        """激活指定的代理配置"""
        try:
            print(f"🔧 激活配置: {profile_name}")
            
            # 回到popup页面
            popup_url = f"chrome-extension://{self.extension_id}/popup.html"
            self.page.get(popup_url)
            time.sleep(2)
            
            # 查找并点击配置
            profile_btn = self.page.ele(f'x://button[contains(text(), "{profile_name}")]', timeout=5)
            if profile_btn:
                profile_btn.click()
                time.sleep(1)
                print(f"✅ 已激活配置: {profile_name}")
                return True
            else:
                print(f"❌ 未找到配置: {profile_name}")
                return False
                
        except Exception as e:
            print(f"❌ 激活配置失败: {e}")
            return False
    
    def test_proxy(self):
        """测试代理连接"""
        try:
            print("\n🔍 测试代理连接...")
            
            # 测试IP
            self.page.get("https://api.ipify.org/")
            time.sleep(3)
            
            ip_content = self.page.html
            import re
            ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
            current_ip = ip_match.group() if ip_match else "未知"
            
            print(f"🌐 当前IP: {current_ip}")
            
            # 测试X.com
            self.page.get("https://x.com/account/access")
            time.sleep(3)
            
            if "x.com" in self.page.url:
                print("✅ X.com访问成功")
                return True
            else:
                print("⚠️ X.com访问失败")
                return False
                
        except Exception as e:
            print(f"❌ 代理测试失败: {e}")
            return False
    
    def run(self):
        """运行代理管理"""
        print("🎮 SwitchyOmega简化代理管理")
        print("🎯 特点: 处理弹窗、避免重复页面、支持动态切换")
        print()

        if not self.create_browser():
            return

        print("⏳ 等待扩展加载...")
        time.sleep(5)  # 给扩展更多时间加载

        # 首先处理欢迎弹窗
        print("🔧 预处理扩展弹窗...")
        self.handle_initial_popup()

        if self.switch_to_proxy(0):
            if self.test_proxy():
                print("\n🎉 代理配置成功！")
                print("💡 您现在可以:")
                print("  🔄 动态切换代理IP")
                print("  🌐 访问任何网站")
                print("  🛡️ 绕过Cloudflare保护")
            else:
                print("\n⚠️ 代理验证失败")
        else:
            print("\n❌ 代理配置失败")

        print("\n🔚 按Enter关闭...")
        input("⏳ 按Enter关闭浏览器...")

        if self.page:
            self.page.quit()

    def handle_initial_popup(self):
        """预处理初始弹窗"""
        try:
            # 访问扩展主页面来触发弹窗
            options_url = f"chrome-extension://{self.extension_id}/options.html"
            self.page.get(options_url)
            time.sleep(2)

            # 处理欢迎弹窗
            self.handle_welcome_popup()

            print("✅ 初始弹窗处理完成")

        except Exception as e:
            print(f"⚠️ 预处理弹窗失败: {e}")
            # 不影响主流程
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            self.page.quit()


def main():
    """主函数"""
    try:
        manager = SwitchyOmegaSimple()
        manager.run()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        if 'manager' in locals():
            manager.close()


if __name__ == "__main__":
    main()
