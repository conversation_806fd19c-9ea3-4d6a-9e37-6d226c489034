(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.OmegaTargetChromium = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
module.exports = require('./lib/heap');

},{"./lib/heap":2}],2:[function(require,module,exports){
// Generated by CoffeeScript 1.8.0
(function() {
  var Heap, defaultCmp, floor, heapify, heappop, heappush, heappushpop, heapreplace, insort, min, nlargest, nsmallest, updateItem, _siftdown, _siftup;

  floor = Math.floor, min = Math.min;


  /*
  Default comparison function to be used
   */

  defaultCmp = function(x, y) {
    if (x < y) {
      return -1;
    }
    if (x > y) {
      return 1;
    }
    return 0;
  };


  /*
  Insert item x in list a, and keep it sorted assuming a is sorted.
  
  If x is already in a, insert it to the right of the rightmost x.
  
  Optional args lo (default 0) and hi (default a.length) bound the slice
  of a to be searched.
   */

  insort = function(a, x, lo, hi, cmp) {
    var mid;
    if (lo == null) {
      lo = 0;
    }
    if (cmp == null) {
      cmp = defaultCmp;
    }
    if (lo < 0) {
      throw new Error('lo must be non-negative');
    }
    if (hi == null) {
      hi = a.length;
    }
    while (lo < hi) {
      mid = floor((lo + hi) / 2);
      if (cmp(x, a[mid]) < 0) {
        hi = mid;
      } else {
        lo = mid + 1;
      }
    }
    return ([].splice.apply(a, [lo, lo - lo].concat(x)), x);
  };


  /*
  Push item onto heap, maintaining the heap invariant.
   */

  heappush = function(array, item, cmp) {
    if (cmp == null) {
      cmp = defaultCmp;
    }
    array.push(item);
    return _siftdown(array, 0, array.length - 1, cmp);
  };


  /*
  Pop the smallest item off the heap, maintaining the heap invariant.
   */

  heappop = function(array, cmp) {
    var lastelt, returnitem;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    lastelt = array.pop();
    if (array.length) {
      returnitem = array[0];
      array[0] = lastelt;
      _siftup(array, 0, cmp);
    } else {
      returnitem = lastelt;
    }
    return returnitem;
  };


  /*
  Pop and return the current smallest value, and add the new item.
  
  This is more efficient than heappop() followed by heappush(), and can be
  more appropriate when using a fixed size heap. Note that the value
  returned may be larger than item! That constrains reasonable use of
  this routine unless written as part of a conditional replacement:
      if item > array[0]
        item = heapreplace(array, item)
   */

  heapreplace = function(array, item, cmp) {
    var returnitem;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    returnitem = array[0];
    array[0] = item;
    _siftup(array, 0, cmp);
    return returnitem;
  };


  /*
  Fast version of a heappush followed by a heappop.
   */

  heappushpop = function(array, item, cmp) {
    var _ref;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    if (array.length && cmp(array[0], item) < 0) {
      _ref = [array[0], item], item = _ref[0], array[0] = _ref[1];
      _siftup(array, 0, cmp);
    }
    return item;
  };


  /*
  Transform list into a heap, in-place, in O(array.length) time.
   */

  heapify = function(array, cmp) {
    var i, _i, _j, _len, _ref, _ref1, _results, _results1;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    _ref1 = (function() {
      _results1 = [];
      for (var _j = 0, _ref = floor(array.length / 2); 0 <= _ref ? _j < _ref : _j > _ref; 0 <= _ref ? _j++ : _j--){ _results1.push(_j); }
      return _results1;
    }).apply(this).reverse();
    _results = [];
    for (_i = 0, _len = _ref1.length; _i < _len; _i++) {
      i = _ref1[_i];
      _results.push(_siftup(array, i, cmp));
    }
    return _results;
  };


  /*
  Update the position of the given item in the heap.
  This function should be called every time the item is being modified.
   */

  updateItem = function(array, item, cmp) {
    var pos;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    pos = array.indexOf(item);
    if (pos === -1) {
      return;
    }
    _siftdown(array, 0, pos, cmp);
    return _siftup(array, pos, cmp);
  };


  /*
  Find the n largest elements in a dataset.
   */

  nlargest = function(array, n, cmp) {
    var elem, result, _i, _len, _ref;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    result = array.slice(0, n);
    if (!result.length) {
      return result;
    }
    heapify(result, cmp);
    _ref = array.slice(n);
    for (_i = 0, _len = _ref.length; _i < _len; _i++) {
      elem = _ref[_i];
      heappushpop(result, elem, cmp);
    }
    return result.sort(cmp).reverse();
  };


  /*
  Find the n smallest elements in a dataset.
   */

  nsmallest = function(array, n, cmp) {
    var elem, i, los, result, _i, _j, _len, _ref, _ref1, _results;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    if (n * 10 <= array.length) {
      result = array.slice(0, n).sort(cmp);
      if (!result.length) {
        return result;
      }
      los = result[result.length - 1];
      _ref = array.slice(n);
      for (_i = 0, _len = _ref.length; _i < _len; _i++) {
        elem = _ref[_i];
        if (cmp(elem, los) < 0) {
          insort(result, elem, 0, null, cmp);
          result.pop();
          los = result[result.length - 1];
        }
      }
      return result;
    }
    heapify(array, cmp);
    _results = [];
    for (i = _j = 0, _ref1 = min(n, array.length); 0 <= _ref1 ? _j < _ref1 : _j > _ref1; i = 0 <= _ref1 ? ++_j : --_j) {
      _results.push(heappop(array, cmp));
    }
    return _results;
  };

  _siftdown = function(array, startpos, pos, cmp) {
    var newitem, parent, parentpos;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    newitem = array[pos];
    while (pos > startpos) {
      parentpos = (pos - 1) >> 1;
      parent = array[parentpos];
      if (cmp(newitem, parent) < 0) {
        array[pos] = parent;
        pos = parentpos;
        continue;
      }
      break;
    }
    return array[pos] = newitem;
  };

  _siftup = function(array, pos, cmp) {
    var childpos, endpos, newitem, rightpos, startpos;
    if (cmp == null) {
      cmp = defaultCmp;
    }
    endpos = array.length;
    startpos = pos;
    newitem = array[pos];
    childpos = 2 * pos + 1;
    while (childpos < endpos) {
      rightpos = childpos + 1;
      if (rightpos < endpos && !(cmp(array[childpos], array[rightpos]) < 0)) {
        childpos = rightpos;
      }
      array[pos] = array[childpos];
      pos = childpos;
      childpos = 2 * pos + 1;
    }
    array[pos] = newitem;
    return _siftdown(array, startpos, pos, cmp);
  };

  Heap = (function() {
    Heap.push = heappush;

    Heap.pop = heappop;

    Heap.replace = heapreplace;

    Heap.pushpop = heappushpop;

    Heap.heapify = heapify;

    Heap.updateItem = updateItem;

    Heap.nlargest = nlargest;

    Heap.nsmallest = nsmallest;

    function Heap(cmp) {
      this.cmp = cmp != null ? cmp : defaultCmp;
      this.nodes = [];
    }

    Heap.prototype.push = function(x) {
      return heappush(this.nodes, x, this.cmp);
    };

    Heap.prototype.pop = function() {
      return heappop(this.nodes, this.cmp);
    };

    Heap.prototype.peek = function() {
      return this.nodes[0];
    };

    Heap.prototype.contains = function(x) {
      return this.nodes.indexOf(x) !== -1;
    };

    Heap.prototype.replace = function(x) {
      return heapreplace(this.nodes, x, this.cmp);
    };

    Heap.prototype.pushpop = function(x) {
      return heappushpop(this.nodes, x, this.cmp);
    };

    Heap.prototype.heapify = function() {
      return heapify(this.nodes, this.cmp);
    };

    Heap.prototype.updateItem = function(x) {
      return updateItem(this.nodes, x, this.cmp);
    };

    Heap.prototype.clear = function() {
      return this.nodes = [];
    };

    Heap.prototype.empty = function() {
      return this.nodes.length === 0;
    };

    Heap.prototype.size = function() {
      return this.nodes.length;
    };

    Heap.prototype.clone = function() {
      var heap;
      heap = new Heap();
      heap.nodes = this.nodes.slice(0);
      return heap;
    };

    Heap.prototype.toArray = function() {
      return this.nodes.slice(0);
    };

    Heap.prototype.insert = Heap.prototype.push;

    Heap.prototype.top = Heap.prototype.peek;

    Heap.prototype.front = Heap.prototype.peek;

    Heap.prototype.has = Heap.prototype.contains;

    Heap.prototype.copy = Heap.prototype.clone;

    return Heap;

  })();

  (function(root, factory) {
    if (typeof define === 'function' && define.amd) {
      return define([], factory);
    } else if (typeof exports === 'object') {
      return module.exports = factory();
    } else {
      return root.Heap = factory();
    }
  })(this, function() {
    return Heap;
  });

}).call(this);

},{}],3:[function(require,module,exports){
(function (global){
/*! https://mths.be/punycode v1.4.1 by @mathias */
;(function(root) {

	/** Detect free variables */
	var freeExports = typeof exports == 'object' && exports &&
		!exports.nodeType && exports;
	var freeModule = typeof module == 'object' && module &&
		!module.nodeType && module;
	var freeGlobal = typeof global == 'object' && global;
	if (
		freeGlobal.global === freeGlobal ||
		freeGlobal.window === freeGlobal ||
		freeGlobal.self === freeGlobal
	) {
		root = freeGlobal;
	}

	/**
	 * The `punycode` object.
	 * @name punycode
	 * @type Object
	 */
	var punycode,

	/** Highest positive signed 32-bit float value */
	maxInt = 2147483647, // aka. 0x7FFFFFFF or 2^31-1

	/** Bootstring parameters */
	base = 36,
	tMin = 1,
	tMax = 26,
	skew = 38,
	damp = 700,
	initialBias = 72,
	initialN = 128, // 0x80
	delimiter = '-', // '\x2D'

	/** Regular expressions */
	regexPunycode = /^xn--/,
	regexNonASCII = /[^\x20-\x7E]/, // unprintable ASCII chars + non-ASCII chars
	regexSeparators = /[\x2E\u3002\uFF0E\uFF61]/g, // RFC 3490 separators

	/** Error messages */
	errors = {
		'overflow': 'Overflow: input needs wider integers to process',
		'not-basic': 'Illegal input >= 0x80 (not a basic code point)',
		'invalid-input': 'Invalid input'
	},

	/** Convenience shortcuts */
	baseMinusTMin = base - tMin,
	floor = Math.floor,
	stringFromCharCode = String.fromCharCode,

	/** Temporary variable */
	key;

	/*--------------------------------------------------------------------------*/

	/**
	 * A generic error utility function.
	 * @private
	 * @param {String} type The error type.
	 * @returns {Error} Throws a `RangeError` with the applicable error message.
	 */
	function error(type) {
		throw new RangeError(errors[type]);
	}

	/**
	 * A generic `Array#map` utility function.
	 * @private
	 * @param {Array} array The array to iterate over.
	 * @param {Function} callback The function that gets called for every array
	 * item.
	 * @returns {Array} A new array of values returned by the callback function.
	 */
	function map(array, fn) {
		var length = array.length;
		var result = [];
		while (length--) {
			result[length] = fn(array[length]);
		}
		return result;
	}

	/**
	 * A simple `Array#map`-like wrapper to work with domain name strings or email
	 * addresses.
	 * @private
	 * @param {String} domain The domain name or email address.
	 * @param {Function} callback The function that gets called for every
	 * character.
	 * @returns {Array} A new string of characters returned by the callback
	 * function.
	 */
	function mapDomain(string, fn) {
		var parts = string.split('@');
		var result = '';
		if (parts.length > 1) {
			// In email addresses, only the domain name should be punycoded. Leave
			// the local part (i.e. everything up to `@`) intact.
			result = parts[0] + '@';
			string = parts[1];
		}
		// Avoid `split(regex)` for IE8 compatibility. See #17.
		string = string.replace(regexSeparators, '\x2E');
		var labels = string.split('.');
		var encoded = map(labels, fn).join('.');
		return result + encoded;
	}

	/**
	 * Creates an array containing the numeric code points of each Unicode
	 * character in the string. While JavaScript uses UCS-2 internally,
	 * this function will convert a pair of surrogate halves (each of which
	 * UCS-2 exposes as separate characters) into a single code point,
	 * matching UTF-16.
	 * @see `punycode.ucs2.encode`
	 * @see <https://mathiasbynens.be/notes/javascript-encoding>
	 * @memberOf punycode.ucs2
	 * @name decode
	 * @param {String} string The Unicode input string (UCS-2).
	 * @returns {Array} The new array of code points.
	 */
	function ucs2decode(string) {
		var output = [],
		    counter = 0,
		    length = string.length,
		    value,
		    extra;
		while (counter < length) {
			value = string.charCodeAt(counter++);
			if (value >= 0xD800 && value <= 0xDBFF && counter < length) {
				// high surrogate, and there is a next character
				extra = string.charCodeAt(counter++);
				if ((extra & 0xFC00) == 0xDC00) { // low surrogate
					output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);
				} else {
					// unmatched surrogate; only append this code unit, in case the next
					// code unit is the high surrogate of a surrogate pair
					output.push(value);
					counter--;
				}
			} else {
				output.push(value);
			}
		}
		return output;
	}

	/**
	 * Creates a string based on an array of numeric code points.
	 * @see `punycode.ucs2.decode`
	 * @memberOf punycode.ucs2
	 * @name encode
	 * @param {Array} codePoints The array of numeric code points.
	 * @returns {String} The new Unicode string (UCS-2).
	 */
	function ucs2encode(array) {
		return map(array, function(value) {
			var output = '';
			if (value > 0xFFFF) {
				value -= 0x10000;
				output += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);
				value = 0xDC00 | value & 0x3FF;
			}
			output += stringFromCharCode(value);
			return output;
		}).join('');
	}

	/**
	 * Converts a basic code point into a digit/integer.
	 * @see `digitToBasic()`
	 * @private
	 * @param {Number} codePoint The basic numeric code point value.
	 * @returns {Number} The numeric value of a basic code point (for use in
	 * representing integers) in the range `0` to `base - 1`, or `base` if
	 * the code point does not represent a value.
	 */
	function basicToDigit(codePoint) {
		if (codePoint - 48 < 10) {
			return codePoint - 22;
		}
		if (codePoint - 65 < 26) {
			return codePoint - 65;
		}
		if (codePoint - 97 < 26) {
			return codePoint - 97;
		}
		return base;
	}

	/**
	 * Converts a digit/integer into a basic code point.
	 * @see `basicToDigit()`
	 * @private
	 * @param {Number} digit The numeric value of a basic code point.
	 * @returns {Number} The basic code point whose value (when used for
	 * representing integers) is `digit`, which needs to be in the range
	 * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is
	 * used; else, the lowercase form is used. The behavior is undefined
	 * if `flag` is non-zero and `digit` has no uppercase form.
	 */
	function digitToBasic(digit, flag) {
		//  0..25 map to ASCII a..z or A..Z
		// 26..35 map to ASCII 0..9
		return digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);
	}

	/**
	 * Bias adaptation function as per section 3.4 of RFC 3492.
	 * https://tools.ietf.org/html/rfc3492#section-3.4
	 * @private
	 */
	function adapt(delta, numPoints, firstTime) {
		var k = 0;
		delta = firstTime ? floor(delta / damp) : delta >> 1;
		delta += floor(delta / numPoints);
		for (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {
			delta = floor(delta / baseMinusTMin);
		}
		return floor(k + (baseMinusTMin + 1) * delta / (delta + skew));
	}

	/**
	 * Converts a Punycode string of ASCII-only symbols to a string of Unicode
	 * symbols.
	 * @memberOf punycode
	 * @param {String} input The Punycode string of ASCII-only symbols.
	 * @returns {String} The resulting string of Unicode symbols.
	 */
	function decode(input) {
		// Don't use UCS-2
		var output = [],
		    inputLength = input.length,
		    out,
		    i = 0,
		    n = initialN,
		    bias = initialBias,
		    basic,
		    j,
		    index,
		    oldi,
		    w,
		    k,
		    digit,
		    t,
		    /** Cached calculation results */
		    baseMinusT;

		// Handle the basic code points: let `basic` be the number of input code
		// points before the last delimiter, or `0` if there is none, then copy
		// the first basic code points to the output.

		basic = input.lastIndexOf(delimiter);
		if (basic < 0) {
			basic = 0;
		}

		for (j = 0; j < basic; ++j) {
			// if it's not a basic code point
			if (input.charCodeAt(j) >= 0x80) {
				error('not-basic');
			}
			output.push(input.charCodeAt(j));
		}

		// Main decoding loop: start just after the last delimiter if any basic code
		// points were copied; start at the beginning otherwise.

		for (index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {

			// `index` is the index of the next character to be consumed.
			// Decode a generalized variable-length integer into `delta`,
			// which gets added to `i`. The overflow checking is easier
			// if we increase `i` as we go, then subtract off its starting
			// value at the end to obtain `delta`.
			for (oldi = i, w = 1, k = base; /* no condition */; k += base) {

				if (index >= inputLength) {
					error('invalid-input');
				}

				digit = basicToDigit(input.charCodeAt(index++));

				if (digit >= base || digit > floor((maxInt - i) / w)) {
					error('overflow');
				}

				i += digit * w;
				t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);

				if (digit < t) {
					break;
				}

				baseMinusT = base - t;
				if (w > floor(maxInt / baseMinusT)) {
					error('overflow');
				}

				w *= baseMinusT;

			}

			out = output.length + 1;
			bias = adapt(i - oldi, out, oldi == 0);

			// `i` was supposed to wrap around from `out` to `0`,
			// incrementing `n` each time, so we'll fix that now:
			if (floor(i / out) > maxInt - n) {
				error('overflow');
			}

			n += floor(i / out);
			i %= out;

			// Insert `n` at position `i` of the output
			output.splice(i++, 0, n);

		}

		return ucs2encode(output);
	}

	/**
	 * Converts a string of Unicode symbols (e.g. a domain name label) to a
	 * Punycode string of ASCII-only symbols.
	 * @memberOf punycode
	 * @param {String} input The string of Unicode symbols.
	 * @returns {String} The resulting Punycode string of ASCII-only symbols.
	 */
	function encode(input) {
		var n,
		    delta,
		    handledCPCount,
		    basicLength,
		    bias,
		    j,
		    m,
		    q,
		    k,
		    t,
		    currentValue,
		    output = [],
		    /** `inputLength` will hold the number of code points in `input`. */
		    inputLength,
		    /** Cached calculation results */
		    handledCPCountPlusOne,
		    baseMinusT,
		    qMinusT;

		// Convert the input in UCS-2 to Unicode
		input = ucs2decode(input);

		// Cache the length
		inputLength = input.length;

		// Initialize the state
		n = initialN;
		delta = 0;
		bias = initialBias;

		// Handle the basic code points
		for (j = 0; j < inputLength; ++j) {
			currentValue = input[j];
			if (currentValue < 0x80) {
				output.push(stringFromCharCode(currentValue));
			}
		}

		handledCPCount = basicLength = output.length;

		// `handledCPCount` is the number of code points that have been handled;
		// `basicLength` is the number of basic code points.

		// Finish the basic string - if it is not empty - with a delimiter
		if (basicLength) {
			output.push(delimiter);
		}

		// Main encoding loop:
		while (handledCPCount < inputLength) {

			// All non-basic code points < n have been handled already. Find the next
			// larger one:
			for (m = maxInt, j = 0; j < inputLength; ++j) {
				currentValue = input[j];
				if (currentValue >= n && currentValue < m) {
					m = currentValue;
				}
			}

			// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,
			// but guard against overflow
			handledCPCountPlusOne = handledCPCount + 1;
			if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {
				error('overflow');
			}

			delta += (m - n) * handledCPCountPlusOne;
			n = m;

			for (j = 0; j < inputLength; ++j) {
				currentValue = input[j];

				if (currentValue < n && ++delta > maxInt) {
					error('overflow');
				}

				if (currentValue == n) {
					// Represent delta as a generalized variable-length integer
					for (q = delta, k = base; /* no condition */; k += base) {
						t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);
						if (q < t) {
							break;
						}
						qMinusT = q - t;
						baseMinusT = base - t;
						output.push(
							stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))
						);
						q = floor(qMinusT / baseMinusT);
					}

					output.push(stringFromCharCode(digitToBasic(q, 0)));
					bias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);
					delta = 0;
					++handledCPCount;
				}
			}

			++delta;
			++n;

		}
		return output.join('');
	}

	/**
	 * Converts a Punycode string representing a domain name or an email address
	 * to Unicode. Only the Punycoded parts of the input will be converted, i.e.
	 * it doesn't matter if you call it on a string that has already been
	 * converted to Unicode.
	 * @memberOf punycode
	 * @param {String} input The Punycoded domain name or email address to
	 * convert to Unicode.
	 * @returns {String} The Unicode representation of the given Punycode
	 * string.
	 */
	function toUnicode(input) {
		return mapDomain(input, function(string) {
			return regexPunycode.test(string)
				? decode(string.slice(4).toLowerCase())
				: string;
		});
	}

	/**
	 * Converts a Unicode string representing a domain name or an email address to
	 * Punycode. Only the non-ASCII parts of the domain name will be converted,
	 * i.e. it doesn't matter if you call it with a domain that's already in
	 * ASCII.
	 * @memberOf punycode
	 * @param {String} input The domain name or email address to convert, as a
	 * Unicode string.
	 * @returns {String} The Punycode representation of the given domain name or
	 * email address.
	 */
	function toASCII(input) {
		return mapDomain(input, function(string) {
			return regexNonASCII.test(string)
				? 'xn--' + encode(string)
				: string;
		});
	}

	/*--------------------------------------------------------------------------*/

	/** Define the public API */
	punycode = {
		/**
		 * A string representing the current Punycode.js version number.
		 * @memberOf punycode
		 * @type String
		 */
		'version': '1.4.1',
		/**
		 * An object of methods to convert from JavaScript's internal character
		 * representation (UCS-2) to Unicode code points, and back.
		 * @see <https://mathiasbynens.be/notes/javascript-encoding>
		 * @memberOf punycode
		 * @type Object
		 */
		'ucs2': {
			'decode': ucs2decode,
			'encode': ucs2encode
		},
		'decode': decode,
		'encode': encode,
		'toASCII': toASCII,
		'toUnicode': toUnicode
	};

	/** Expose `punycode` */
	// Some AMD build optimizers, like r.js, check for specific condition patterns
	// like the following:
	if (
		typeof define == 'function' &&
		typeof define.amd == 'object' &&
		define.amd
	) {
		define('punycode', function() {
			return punycode;
		});
	} else if (freeExports && freeModule) {
		if (module.exports == freeExports) {
			// in Node.js, io.js, or RingoJS v0.8.0+
			freeModule.exports = punycode;
		} else {
			// in Narwhal or RingoJS v0.7.0-
			for (key in punycode) {
				punycode.hasOwnProperty(key) && (freeExports[key] = punycode[key]);
			}
		}
	} else {
		// in Rhino or a web browser
		root.punycode = punycode;
	}

}(this));

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
},{}],4:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

'use strict';

// If obj.hasOwnProperty has been overridden, then calling
// obj.hasOwnProperty(prop) will break.
// See: https://github.com/joyent/node/issues/1707
function hasOwnProperty(obj, prop) {
  return Object.prototype.hasOwnProperty.call(obj, prop);
}

module.exports = function(qs, sep, eq, options) {
  sep = sep || '&';
  eq = eq || '=';
  var obj = {};

  if (typeof qs !== 'string' || qs.length === 0) {
    return obj;
  }

  var regexp = /\+/g;
  qs = qs.split(sep);

  var maxKeys = 1000;
  if (options && typeof options.maxKeys === 'number') {
    maxKeys = options.maxKeys;
  }

  var len = qs.length;
  // maxKeys <= 0 means that we should not limit keys count
  if (maxKeys > 0 && len > maxKeys) {
    len = maxKeys;
  }

  for (var i = 0; i < len; ++i) {
    var x = qs[i].replace(regexp, '%20'),
        idx = x.indexOf(eq),
        kstr, vstr, k, v;

    if (idx >= 0) {
      kstr = x.substr(0, idx);
      vstr = x.substr(idx + 1);
    } else {
      kstr = x;
      vstr = '';
    }

    k = decodeURIComponent(kstr);
    v = decodeURIComponent(vstr);

    if (!hasOwnProperty(obj, k)) {
      obj[k] = v;
    } else if (isArray(obj[k])) {
      obj[k].push(v);
    } else {
      obj[k] = [obj[k], v];
    }
  }

  return obj;
};

var isArray = Array.isArray || function (xs) {
  return Object.prototype.toString.call(xs) === '[object Array]';
};

},{}],5:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

'use strict';

var stringifyPrimitive = function(v) {
  switch (typeof v) {
    case 'string':
      return v;

    case 'boolean':
      return v ? 'true' : 'false';

    case 'number':
      return isFinite(v) ? v : '';

    default:
      return '';
  }
};

module.exports = function(obj, sep, eq, name) {
  sep = sep || '&';
  eq = eq || '=';
  if (obj === null) {
    obj = undefined;
  }

  if (typeof obj === 'object') {
    return map(objectKeys(obj), function(k) {
      var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
      if (isArray(obj[k])) {
        return map(obj[k], function(v) {
          return ks + encodeURIComponent(stringifyPrimitive(v));
        }).join(sep);
      } else {
        return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
      }
    }).join(sep);

  }

  if (!name) return '';
  return encodeURIComponent(stringifyPrimitive(name)) + eq +
         encodeURIComponent(stringifyPrimitive(obj));
};

var isArray = Array.isArray || function (xs) {
  return Object.prototype.toString.call(xs) === '[object Array]';
};

function map (xs, f) {
  if (xs.map) return xs.map(f);
  var res = [];
  for (var i = 0; i < xs.length; i++) {
    res.push(f(xs[i], i));
  }
  return res;
}

var objectKeys = Object.keys || function (obj) {
  var res = [];
  for (var key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) res.push(key);
  }
  return res;
};

},{}],6:[function(require,module,exports){
'use strict';

exports.decode = exports.parse = require('./decode');
exports.encode = exports.stringify = require('./encode');

},{"./decode":4,"./encode":5}],7:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

var punycode = require('punycode');

exports.parse = urlParse;
exports.resolve = urlResolve;
exports.resolveObject = urlResolveObject;
exports.format = urlFormat;

exports.Url = Url;

function Url() {
  this.protocol = null;
  this.slashes = null;
  this.auth = null;
  this.host = null;
  this.port = null;
  this.hostname = null;
  this.hash = null;
  this.search = null;
  this.query = null;
  this.pathname = null;
  this.path = null;
  this.href = null;
}

// Reference: RFC 3986, RFC 1808, RFC 2396

// define these here so at least they only have to be
// compiled once on the first module load.
var protocolPattern = /^([a-z0-9.+-]+:)/i,
    portPattern = /:[0-9]*$/,

    // RFC 2396: characters reserved for delimiting URLs.
    // We actually just auto-escape these.
    delims = ['<', '>', '"', '`', ' ', '\r', '\n', '\t'],

    // RFC 2396: characters not allowed for various reasons.
    unwise = ['{', '}', '|', '\\', '^', '`'].concat(delims),

    // Allowed by RFCs, but cause of XSS attacks.  Always escape these.
    autoEscape = ['\''].concat(unwise),
    // Characters that are never ever allowed in a hostname.
    // Note that any invalid chars are also handled, but these
    // are the ones that are *expected* to be seen, so we fast-path
    // them.
    nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape),
    hostEndingChars = ['/', '?', '#'],
    hostnameMaxLen = 255,
    hostnamePartPattern = /^[a-z0-9A-Z_-]{0,63}$/,
    hostnamePartStart = /^([a-z0-9A-Z_-]{0,63})(.*)$/,
    // protocols that can allow "unsafe" and "unwise" chars.
    unsafeProtocol = {
      'javascript': true,
      'javascript:': true
    },
    // protocols that never have a hostname.
    hostlessProtocol = {
      'javascript': true,
      'javascript:': true
    },
    // protocols that always contain a // bit.
    slashedProtocol = {
      'http': true,
      'https': true,
      'ftp': true,
      'gopher': true,
      'file': true,
      'http:': true,
      'https:': true,
      'ftp:': true,
      'gopher:': true,
      'file:': true
    },
    querystring = require('querystring');

function urlParse(url, parseQueryString, slashesDenoteHost) {
  if (url && isObject(url) && url instanceof Url) return url;

  var u = new Url;
  u.parse(url, parseQueryString, slashesDenoteHost);
  return u;
}

Url.prototype.parse = function(url, parseQueryString, slashesDenoteHost) {
  if (!isString(url)) {
    throw new TypeError("Parameter 'url' must be a string, not " + typeof url);
  }

  var rest = url;

  // trim before proceeding.
  // This is to support parse stuff like "  http://foo.com  \n"
  rest = rest.trim();

  var proto = protocolPattern.exec(rest);
  if (proto) {
    proto = proto[0];
    var lowerProto = proto.toLowerCase();
    this.protocol = lowerProto;
    rest = rest.substr(proto.length);
  }

  // figure out if it's got a host
  // user@server is *always* interpreted as a hostname, and url
  // resolution will treat //foo/bar as host=foo,path=bar because that's
  // how the browser resolves relative URLs.
  if (slashesDenoteHost || proto || rest.match(/^\/\/[^@\/]+@[^@\/]+/)) {
    var slashes = rest.substr(0, 2) === '//';
    if (slashes && !(proto && hostlessProtocol[proto])) {
      rest = rest.substr(2);
      this.slashes = true;
    }
  }

  if (!hostlessProtocol[proto] &&
      (slashes || (proto && !slashedProtocol[proto]))) {

    // there's a hostname.
    // the first instance of /, ?, ;, or # ends the host.
    //
    // If there is an @ in the hostname, then non-host chars *are* allowed
    // to the left of the last @ sign, unless some host-ending character
    // comes *before* the @-sign.
    // URLs are obnoxious.
    //
    // ex:
    // http://a@b@c/ => user:a@b host:c
    // http://a@b?@c => user:a host:c path:/?@c

    // v0.12 TODO(isaacs): This is not quite how Chrome does things.
    // Review our test case against browsers more comprehensively.

    // find the first instance of any hostEndingChars
    var hostEnd = -1;
    for (var i = 0; i < hostEndingChars.length; i++) {
      var hec = rest.indexOf(hostEndingChars[i]);
      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd))
        hostEnd = hec;
    }

    // at this point, either we have an explicit point where the
    // auth portion cannot go past, or the last @ char is the decider.
    var auth, atSign;
    if (hostEnd === -1) {
      // atSign can be anywhere.
      atSign = rest.lastIndexOf('@');
    } else {
      // atSign must be in auth portion.
      // http://a@b/c@d => host:b auth:a path:/c@d
      atSign = rest.lastIndexOf('@', hostEnd);
    }

    // Now we have a portion which is definitely the auth.
    // Pull that off.
    if (atSign !== -1) {
      auth = rest.slice(0, atSign);
      rest = rest.slice(atSign + 1);
      this.auth = decodeURIComponent(auth);
    }

    // the host is the remaining to the left of the first non-host char
    hostEnd = -1;
    for (var i = 0; i < nonHostChars.length; i++) {
      var hec = rest.indexOf(nonHostChars[i]);
      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd))
        hostEnd = hec;
    }
    // if we still have not hit it, then the entire thing is a host.
    if (hostEnd === -1)
      hostEnd = rest.length;

    this.host = rest.slice(0, hostEnd);
    rest = rest.slice(hostEnd);

    // pull out port.
    this.parseHost();

    // we've indicated that there is a hostname,
    // so even if it's empty, it has to be present.
    this.hostname = this.hostname || '';

    // if hostname begins with [ and ends with ]
    // assume that it's an IPv6 address.
    var ipv6Hostname = this.hostname[0] === '[' &&
        this.hostname[this.hostname.length - 1] === ']';

    // validate a little.
    if (!ipv6Hostname) {
      var hostparts = this.hostname.split(/\./);
      for (var i = 0, l = hostparts.length; i < l; i++) {
        var part = hostparts[i];
        if (!part) continue;
        if (!part.match(hostnamePartPattern)) {
          var newpart = '';
          for (var j = 0, k = part.length; j < k; j++) {
            if (part.charCodeAt(j) > 127) {
              // we replace non-ASCII char with a temporary placeholder
              // we need this to make sure size of hostname is not
              // broken by replacing non-ASCII by nothing
              newpart += 'x';
            } else {
              newpart += part[j];
            }
          }
          // we test again with ASCII char only
          if (!newpart.match(hostnamePartPattern)) {
            var validParts = hostparts.slice(0, i);
            var notHost = hostparts.slice(i + 1);
            var bit = part.match(hostnamePartStart);
            if (bit) {
              validParts.push(bit[1]);
              notHost.unshift(bit[2]);
            }
            if (notHost.length) {
              rest = '/' + notHost.join('.') + rest;
            }
            this.hostname = validParts.join('.');
            break;
          }
        }
      }
    }

    if (this.hostname.length > hostnameMaxLen) {
      this.hostname = '';
    } else {
      // hostnames are always lower case.
      this.hostname = this.hostname.toLowerCase();
    }

    if (!ipv6Hostname) {
      // IDNA Support: Returns a puny coded representation of "domain".
      // It only converts the part of the domain name that
      // has non ASCII characters. I.e. it dosent matter if
      // you call it with a domain that already is in ASCII.
      var domainArray = this.hostname.split('.');
      var newOut = [];
      for (var i = 0; i < domainArray.length; ++i) {
        var s = domainArray[i];
        newOut.push(s.match(/[^A-Za-z0-9_-]/) ?
            'xn--' + punycode.encode(s) : s);
      }
      this.hostname = newOut.join('.');
    }

    var p = this.port ? ':' + this.port : '';
    var h = this.hostname || '';
    this.host = h + p;
    this.href += this.host;

    // strip [ and ] from the hostname
    // the host field still retains them, though
    if (ipv6Hostname) {
      this.hostname = this.hostname.substr(1, this.hostname.length - 2);
      if (rest[0] !== '/') {
        rest = '/' + rest;
      }
    }
  }

  // now rest is set to the post-host stuff.
  // chop off any delim chars.
  if (!unsafeProtocol[lowerProto]) {

    // First, make 100% sure that any "autoEscape" chars get
    // escaped, even if encodeURIComponent doesn't think they
    // need to be.
    for (var i = 0, l = autoEscape.length; i < l; i++) {
      var ae = autoEscape[i];
      var esc = encodeURIComponent(ae);
      if (esc === ae) {
        esc = escape(ae);
      }
      rest = rest.split(ae).join(esc);
    }
  }


  // chop off from the tail first.
  var hash = rest.indexOf('#');
  if (hash !== -1) {
    // got a fragment string.
    this.hash = rest.substr(hash);
    rest = rest.slice(0, hash);
  }
  var qm = rest.indexOf('?');
  if (qm !== -1) {
    this.search = rest.substr(qm);
    this.query = rest.substr(qm + 1);
    if (parseQueryString) {
      this.query = querystring.parse(this.query);
    }
    rest = rest.slice(0, qm);
  } else if (parseQueryString) {
    // no query string, but parseQueryString still requested
    this.search = '';
    this.query = {};
  }
  if (rest) this.pathname = rest;
  if (slashedProtocol[lowerProto] &&
      this.hostname && !this.pathname) {
    this.pathname = '/';
  }

  //to support http.request
  if (this.pathname || this.search) {
    var p = this.pathname || '';
    var s = this.search || '';
    this.path = p + s;
  }

  // finally, reconstruct the href based on what has been validated.
  this.href = this.format();
  return this;
};

// format a parsed object into a url string
function urlFormat(obj) {
  // ensure it's an object, and not a string url.
  // If it's an obj, this is a no-op.
  // this way, you can call url_format() on strings
  // to clean up potentially wonky urls.
  if (isString(obj)) obj = urlParse(obj);
  if (!(obj instanceof Url)) return Url.prototype.format.call(obj);
  return obj.format();
}

Url.prototype.format = function() {
  var auth = this.auth || '';
  if (auth) {
    auth = encodeURIComponent(auth);
    auth = auth.replace(/%3A/i, ':');
    auth += '@';
  }

  var protocol = this.protocol || '',
      pathname = this.pathname || '',
      hash = this.hash || '',
      host = false,
      query = '';

  if (this.host) {
    host = auth + this.host;
  } else if (this.hostname) {
    host = auth + (this.hostname.indexOf(':') === -1 ?
        this.hostname :
        '[' + this.hostname + ']');
    if (this.port) {
      host += ':' + this.port;
    }
  }

  if (this.query &&
      isObject(this.query) &&
      Object.keys(this.query).length) {
    query = querystring.stringify(this.query);
  }

  var search = this.search || (query && ('?' + query)) || '';

  if (protocol && protocol.substr(-1) !== ':') protocol += ':';

  // only the slashedProtocols get the //.  Not mailto:, xmpp:, etc.
  // unless they had them to begin with.
  if (this.slashes ||
      (!protocol || slashedProtocol[protocol]) && host !== false) {
    host = '//' + (host || '');
    if (pathname && pathname.charAt(0) !== '/') pathname = '/' + pathname;
  } else if (!host) {
    host = '';
  }

  if (hash && hash.charAt(0) !== '#') hash = '#' + hash;
  if (search && search.charAt(0) !== '?') search = '?' + search;

  pathname = pathname.replace(/[?#]/g, function(match) {
    return encodeURIComponent(match);
  });
  search = search.replace('#', '%23');

  return protocol + host + pathname + search + hash;
};

function urlResolve(source, relative) {
  return urlParse(source, false, true).resolve(relative);
}

Url.prototype.resolve = function(relative) {
  return this.resolveObject(urlParse(relative, false, true)).format();
};

function urlResolveObject(source, relative) {
  if (!source) return relative;
  return urlParse(source, false, true).resolveObject(relative);
}

Url.prototype.resolveObject = function(relative) {
  if (isString(relative)) {
    var rel = new Url();
    rel.parse(relative, false, true);
    relative = rel;
  }

  var result = new Url();
  Object.keys(this).forEach(function(k) {
    result[k] = this[k];
  }, this);

  // hash is always overridden, no matter what.
  // even href="" will remove it.
  result.hash = relative.hash;

  // if the relative url is empty, then there's nothing left to do here.
  if (relative.href === '') {
    result.href = result.format();
    return result;
  }

  // hrefs like //foo/bar always cut to the protocol.
  if (relative.slashes && !relative.protocol) {
    // take everything except the protocol from relative
    Object.keys(relative).forEach(function(k) {
      if (k !== 'protocol')
        result[k] = relative[k];
    });

    //urlParse appends trailing / to urls like http://www.example.com
    if (slashedProtocol[result.protocol] &&
        result.hostname && !result.pathname) {
      result.path = result.pathname = '/';
    }

    result.href = result.format();
    return result;
  }

  if (relative.protocol && relative.protocol !== result.protocol) {
    // if it's a known url protocol, then changing
    // the protocol does weird things
    // first, if it's not file:, then we MUST have a host,
    // and if there was a path
    // to begin with, then we MUST have a path.
    // if it is file:, then the host is dropped,
    // because that's known to be hostless.
    // anything else is assumed to be absolute.
    if (!slashedProtocol[relative.protocol]) {
      Object.keys(relative).forEach(function(k) {
        result[k] = relative[k];
      });
      result.href = result.format();
      return result;
    }

    result.protocol = relative.protocol;
    if (!relative.host && !hostlessProtocol[relative.protocol]) {
      var relPath = (relative.pathname || '').split('/');
      while (relPath.length && !(relative.host = relPath.shift()));
      if (!relative.host) relative.host = '';
      if (!relative.hostname) relative.hostname = '';
      if (relPath[0] !== '') relPath.unshift('');
      if (relPath.length < 2) relPath.unshift('');
      result.pathname = relPath.join('/');
    } else {
      result.pathname = relative.pathname;
    }
    result.search = relative.search;
    result.query = relative.query;
    result.host = relative.host || '';
    result.auth = relative.auth;
    result.hostname = relative.hostname || relative.host;
    result.port = relative.port;
    // to support http.request
    if (result.pathname || result.search) {
      var p = result.pathname || '';
      var s = result.search || '';
      result.path = p + s;
    }
    result.slashes = result.slashes || relative.slashes;
    result.href = result.format();
    return result;
  }

  var isSourceAbs = (result.pathname && result.pathname.charAt(0) === '/'),
      isRelAbs = (
          relative.host ||
          relative.pathname && relative.pathname.charAt(0) === '/'
      ),
      mustEndAbs = (isRelAbs || isSourceAbs ||
                    (result.host && relative.pathname)),
      removeAllDots = mustEndAbs,
      srcPath = result.pathname && result.pathname.split('/') || [],
      relPath = relative.pathname && relative.pathname.split('/') || [],
      psychotic = result.protocol && !slashedProtocol[result.protocol];

  // if the url is a non-slashed url, then relative
  // links like ../.. should be able
  // to crawl up to the hostname, as well.  This is strange.
  // result.protocol has already been set by now.
  // Later on, put the first path part into the host field.
  if (psychotic) {
    result.hostname = '';
    result.port = null;
    if (result.host) {
      if (srcPath[0] === '') srcPath[0] = result.host;
      else srcPath.unshift(result.host);
    }
    result.host = '';
    if (relative.protocol) {
      relative.hostname = null;
      relative.port = null;
      if (relative.host) {
        if (relPath[0] === '') relPath[0] = relative.host;
        else relPath.unshift(relative.host);
      }
      relative.host = null;
    }
    mustEndAbs = mustEndAbs && (relPath[0] === '' || srcPath[0] === '');
  }

  if (isRelAbs) {
    // it's absolute.
    result.host = (relative.host || relative.host === '') ?
                  relative.host : result.host;
    result.hostname = (relative.hostname || relative.hostname === '') ?
                      relative.hostname : result.hostname;
    result.search = relative.search;
    result.query = relative.query;
    srcPath = relPath;
    // fall through to the dot-handling below.
  } else if (relPath.length) {
    // it's relative
    // throw away the existing file, and take the new path instead.
    if (!srcPath) srcPath = [];
    srcPath.pop();
    srcPath = srcPath.concat(relPath);
    result.search = relative.search;
    result.query = relative.query;
  } else if (!isNullOrUndefined(relative.search)) {
    // just pull out the search.
    // like href='?foo'.
    // Put this after the other two cases because it simplifies the booleans
    if (psychotic) {
      result.hostname = result.host = srcPath.shift();
      //occationaly the auth can get stuck only in host
      //this especialy happens in cases like
      //url.resolveObject('mailto:local1@domain1', 'local2@domain2')
      var authInHost = result.host && result.host.indexOf('@') > 0 ?
                       result.host.split('@') : false;
      if (authInHost) {
        result.auth = authInHost.shift();
        result.host = result.hostname = authInHost.shift();
      }
    }
    result.search = relative.search;
    result.query = relative.query;
    //to support http.request
    if (!isNull(result.pathname) || !isNull(result.search)) {
      result.path = (result.pathname ? result.pathname : '') +
                    (result.search ? result.search : '');
    }
    result.href = result.format();
    return result;
  }

  if (!srcPath.length) {
    // no path at all.  easy.
    // we've already handled the other stuff above.
    result.pathname = null;
    //to support http.request
    if (result.search) {
      result.path = '/' + result.search;
    } else {
      result.path = null;
    }
    result.href = result.format();
    return result;
  }

  // if a url ENDs in . or .., then it must get a trailing slash.
  // however, if it ends in anything else non-slashy,
  // then it must NOT get a trailing slash.
  var last = srcPath.slice(-1)[0];
  var hasTrailingSlash = (
      (result.host || relative.host) && (last === '.' || last === '..') ||
      last === '');

  // strip single dots, resolve double dots to parent dir
  // if the path tries to go above the root, `up` ends up > 0
  var up = 0;
  for (var i = srcPath.length; i >= 0; i--) {
    last = srcPath[i];
    if (last == '.') {
      srcPath.splice(i, 1);
    } else if (last === '..') {
      srcPath.splice(i, 1);
      up++;
    } else if (up) {
      srcPath.splice(i, 1);
      up--;
    }
  }

  // if the path is allowed to go above the root, restore leading ..s
  if (!mustEndAbs && !removeAllDots) {
    for (; up--; up) {
      srcPath.unshift('..');
    }
  }

  if (mustEndAbs && srcPath[0] !== '' &&
      (!srcPath[0] || srcPath[0].charAt(0) !== '/')) {
    srcPath.unshift('');
  }

  if (hasTrailingSlash && (srcPath.join('/').substr(-1) !== '/')) {
    srcPath.push('');
  }

  var isAbsolute = srcPath[0] === '' ||
      (srcPath[0] && srcPath[0].charAt(0) === '/');

  // put the host back
  if (psychotic) {
    result.hostname = result.host = isAbsolute ? '' :
                                    srcPath.length ? srcPath.shift() : '';
    //occationaly the auth can get stuck only in host
    //this especialy happens in cases like
    //url.resolveObject('mailto:local1@domain1', 'local2@domain2')
    var authInHost = result.host && result.host.indexOf('@') > 0 ?
                     result.host.split('@') : false;
    if (authInHost) {
      result.auth = authInHost.shift();
      result.host = result.hostname = authInHost.shift();
    }
  }

  mustEndAbs = mustEndAbs || (result.host && srcPath.length);

  if (mustEndAbs && !isAbsolute) {
    srcPath.unshift('');
  }

  if (!srcPath.length) {
    result.pathname = null;
    result.path = null;
  } else {
    result.pathname = srcPath.join('/');
  }

  //to support request.http
  if (!isNull(result.pathname) || !isNull(result.search)) {
    result.path = (result.pathname ? result.pathname : '') +
                  (result.search ? result.search : '');
  }
  result.auth = relative.auth || result.auth;
  result.slashes = result.slashes || relative.slashes;
  result.href = result.format();
  return result;
};

Url.prototype.parseHost = function() {
  var host = this.host;
  var port = portPattern.exec(host);
  if (port) {
    port = port[0];
    if (port !== ':') {
      this.port = port.substr(1);
    }
    host = host.substr(0, host.length - port.length);
  }
  if (host) this.hostname = host;
};

function isString(arg) {
  return typeof arg === "string";
}

function isObject(arg) {
  return typeof arg === 'object' && arg !== null;
}

function isNull(arg) {
  return arg === null;
}
function isNullOrUndefined(arg) {
  return  arg == null;
}

},{"punycode":3,"querystring":6}],8:[function(require,module,exports){
module.exports = OmegaTarget

},{}],9:[function(require,module,exports){
var OmegaTarget, Promise,
  slice = [].slice;

OmegaTarget = require('omega-target');

Promise = OmegaTarget.Promise;

exports.chromeApiPromisify = function(target, method) {
  return function() {
    var args;
    args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
    return new Promise(function(resolve, reject) {
      var callback;
      callback = function() {
        var callbackArgs, error;
        callbackArgs = 1 <= arguments.length ? slice.call(arguments, 0) : [];
        if (chrome.runtime.lastError != null) {
          error = new Error(chrome.runtime.lastError.message);
          error.original = chrome.runtime.lastError;
          return reject(error);
        }
        if (callbackArgs.length <= 1) {
          return resolve(callbackArgs[0]);
        } else {
          return resolve(callbackArgs);
        }
      };
      args.push(callback);
      return target[method].apply(target, args);
    });
  };
};


},{"omega-target":8}],10:[function(require,module,exports){
var ChromePort, TrackedEvent,
  slice = [].slice;

module.exports = ChromePort = (function() {
  function ChromePort(port) {
    this.port = port;
    this.name = this.port.name;
    this.sender = this.port.sender;
    this.disconnect = this.port.disconnect.bind(this.port);
    this.postMessage = (function(_this) {
      return function() {
        var _, args, ref;
        args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
        try {
          return (ref = _this.port).postMessage.apply(ref, args);
        } catch (error) {
          _ = error;
        }
      };
    })(this);
    this.onMessage = new TrackedEvent(this.port.onMessage);
    this.onDisconnect = new TrackedEvent(this.port.onDisconnect);
    this.onDisconnect.addListener(this.dispose.bind(this));
  }

  ChromePort.prototype.dispose = function() {
    this.onMessage.dispose();
    return this.onDisconnect.dispose();
  };

  return ChromePort;

})();

TrackedEvent = (function() {
  function TrackedEvent(event) {
    var j, len, mes, method, methodName;
    this.event = event;
    this.callbacks = [];
    mes = ['hasListener', 'hasListeners', 'addRules', 'getRules', 'removeRules'];
    for (j = 0, len = mes.length; j < len; j++) {
      methodName = mes[j];
      method = this.event[methodName];
      if (method != null) {
        this[methodName] = method.bind(this.event);
      }
    }
  }

  TrackedEvent.prototype.addListener = function(callback) {
    this.event.addListener(callback);
    this.callbacks.push(callback);
    return this;
  };

  TrackedEvent.prototype.removeListener = function(callback) {
    var i;
    this.event.removeListener(callback);
    i = this.callbacks.indexOf(callback);
    if (i >= 0) {
      this.callbacks.splice(i, 1);
    }
    return this;
  };


  /**
   * Removes all listeners added via this TrackedEvent instance.
   * Note: Won't remove listeners added via other TrackedEvent or raw Event.
   */

  TrackedEvent.prototype.removeAllListeners = function() {
    var callback, j, len, ref;
    ref = this.callbacks;
    for (j = 0, len = ref.length; j < len; j++) {
      callback = ref[j];
      this.event.removeListener(callback);
    }
    this.callbacks = [];
    return this;
  };


  /**
   * Removes all listeners added via this TrackedEvent instance and prevent any
   * further listeners from being added. It is considered safe to nullify any
   * references to this instance and the underlying Event without causing leaks.
   * This should be the last method called in the lifetime of TrackedEvent.
   *
   * Throws if the underlying raw Event object still has listeners. This can
   * happen when listeners have been added via other TrackedEvents or raw Event.
   */

  TrackedEvent.prototype.dispose = function() {
    var base;
    this.removeAllListeners();
    if (typeof (base = this.event).hasListeners === "function" ? base.hasListeners() : void 0) {
      throw new Error("Underlying Event still has listeners!");
    }
    this.event = null;
    return this.callbacks = null;
  };

  return TrackedEvent;

})();


},{}],11:[function(require,module,exports){
var ChromePort, ExternalApi, OmegaPac, OmegaTarget, Promise;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

ChromePort = require('./chrome_port');

module.exports = ExternalApi = (function() {
  function ExternalApi(options) {
    this.options = options;
  }

  ExternalApi.prototype.knownExts = {
    'padekgcemlokbadohgkifijomclgjgif': 32
  };

  ExternalApi.prototype.disabled = false;

  ExternalApi.prototype.listen = function() {
    if (!chrome.runtime.onConnectExternal) {
      return;
    }
    return chrome.runtime.onConnectExternal.addListener((function(_this) {
      return function(rawPort) {
        var port;
        port = new ChromePort(rawPort);
        port.onMessage.addListener(function(msg) {
          return _this.onMessage(msg, port);
        });
        return port.onDisconnect.addListener(_this.reenable.bind(_this));
      };
    })(this));
  };

  ExternalApi.prototype._previousProfileName = null;

  ExternalApi.prototype.reenable = function() {
    var base;
    if (!this.disabled) {
      return;
    }
    this.options.setProxyNotControllable(null);
    if (typeof (base = chrome.action).setPopup === "function") {
      base.setPopup({
        popup: globalThis.POPUPHTMLURL
      });
    }
    this.options.reloadQuickSwitch();
    this.disabled = false;
    this.options.clearBadge();
    return this.options.applyProfile(this._previousProfileName);
  };

  ExternalApi.prototype.checkPerm = function(port, level) {
    var perm;
    perm = this.knownExts[port.sender.id] || 0;
    if (perm < level) {
      port.postMessage({
        action: 'error',
        error: 'permission'
      });
      return false;
    } else {
      return true;
    }
  };

  ExternalApi.prototype.onMessage = function(msg, port) {
    var base, ref;
    this.options.log.log(port.sender.id + " -> " + msg.action, msg);
    switch (msg.action) {
      case 'disable':
        if (!this.checkPerm(port, 16)) {
          return;
        }
        if (this.disabled) {
          return;
        }
        this.disabled = true;
        this._previousProfileName = ((ref = this.options.currentProfile()) != null ? ref.name : void 0) || 'system';
        this.options.applyProfile('system').then((function(_this) {
          return function() {
            var reason;
            reason = 'disabled';
            if (_this.knownExts[port.sender.id] >= 32) {
              reason = 'upgrade';
            }
            return _this.options.setProxyNotControllable(reason, {
              text: 'X',
              color: '#5ab432'
            });
          };
        })(this));
        if (typeof (base = chrome.action).setPopup === "function") {
          base.setPopup({
            popup: 'popup-iframe.html'
          });
        }
        return port.postMessage({
          action: 'state',
          state: 'disabled'
        });
      case 'enable':
        this.reenable();
        return port.postMessage({
          action: 'state',
          state: 'enabled'
        });
      case 'getOptions':
        if (!this.checkPerm(port, 8)) {
          return;
        }
        return port.postMessage({
          action: 'options',
          options: this.options.getAll()
        });
      default:
        return port.postMessage({
          action: 'error',
          error: 'noSuchAction',
          action_name: msg.action
        });
    }
  };

  return ExternalApi;

})();


},{"./chrome_port":10,"omega-target":8}],12:[function(require,module,exports){
var ContentTypeRejectedError, Promise, Url, defaultHintHandler, fetchUrl, hintHandlers, xhrWrapper,
  slice = [].slice;

Promise = OmegaTarget.Promise;

Url = require('url');

ContentTypeRejectedError = OmegaTarget.ContentTypeRejectedError;

xhrWrapper = function() {
  var args;
  args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
  return fetch.apply(null, args).then(function(response) {
    return response.text().then(function(body) {
      return [response, body];
    });
  })["catch"](function(err) {
    if (!err.isOperational) {
      throw err;
    }
    if (!err.statusCode) {
      throw new OmegaTarget.NetworkError(err);
    }
    if (err.statusCode === 404) {
      throw new OmegaTarget.HttpNotFoundError(err);
    }
    if (err.statusCode >= 500 && err.statusCode < 600) {
      throw new OmegaTarget.HttpServerError(err);
    }
    throw new OmegaTarget.HttpError(err);
  });
};

fetchUrl = function(dest_url, opt_bypass_cache, opt_type_hints) {
  var dest_url_nocache, getResBody, parsed;
  getResBody = function(arg) {
    var body, contentType, handler, hint, i, len, ref, ref1, response, result;
    response = arg[0], body = arg[1];
    if (!opt_type_hints) {
      return body;
    }
    contentType = (ref = response.headers['content-type']) != null ? ref.toLowerCase() : void 0;
    for (i = 0, len = opt_type_hints.length; i < len; i++) {
      hint = opt_type_hints[i];
      handler = (ref1 = hintHandlers[hint]) != null ? ref1 : defaultHintHandler;
      result = handler(response, body, {
        contentType: contentType,
        hint: hint
      });
      if (result != null) {
        return result;
      }
    }
    throw new ContentTypeRejectedError('Unrecognized Content-Type: ' + contentType);
    return body;
  };
  if (opt_bypass_cache && dest_url.indexOf('?') < 0) {
    parsed = Url.parse(dest_url, true);
    parsed.search = void 0;
    parsed.query['_'] = Date.now();
    dest_url_nocache = Url.format(parsed);
    return xhrWrapper(dest_url_nocache).then(getResBody)["catch"](function() {
      return xhrWrapper(dest_url).then(getResBody);
    });
  } else {
    return xhrWrapper(dest_url).then(getResBody);
  }
};

defaultHintHandler = function(response, body, arg) {
  var contentType, hint;
  contentType = arg.contentType, hint = arg.hint;
  if ('!' + contentType === hint) {
    throw new ContentTypeRejectedError('Response Content-Type blacklisted: ' + contentType);
  }
  if (contentType === hint) {
    return body;
  }
};

hintHandlers = {
  '*': function(response, body) {
    return body;
  },
  '!text/html': function(response, body, arg) {
    var contentType, hint, looksLikeHtml;
    contentType = arg.contentType, hint = arg.hint;
    if (contentType === hint) {
      looksLikeHtml = false;
      if (body.indexOf('<!DOCTYPE') >= 0 || body.indexOf('<!doctype') >= 0) {
        looksLikeHtml = true;
      } else if (body.indexOf('</html>') >= 0) {
        looksLikeHtml = true;
      } else if (body.indexOf('</body>') >= 0) {
        looksLikeHtml = true;
      }
      if (looksLikeHtml) {
        throw new ContentTypeRejectedError('Response must not be HTML.');
      }
    }
  },
  '!application/xhtml+xml': function() {
    var args;
    args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
    return hintHandlers['!text/html'].apply(hintHandlers, args);
  },
  'application/x-ns-proxy-autoconfig': function(response, body, arg) {
    var contentType, hint;
    contentType = arg.contentType, hint = arg.hint;
    if (contentType === hint) {
      return body;
    }
    if (body.indexOf('FindProxyForURL') >= 0) {
      return body;
    } else {
      return void 0;
    }
  }
};

module.exports = fetchUrl;


},{"url":7}],13:[function(require,module,exports){
var base, name, ref, value;

module.exports = {
  Storage: require('./storage'),
  SyncStorage: require('./sync_storage'),
  Options: require('./options'),
  ChromeTabs: require('./tabs'),
  SwitchySharp: require('./switchysharp'),
  ExternalApi: require('./external_api'),
  WebRequestMonitor: require('./web_request_monitor'),
  Inspect: require('./inspect'),
  Url: require('url'),
  proxy: require('./proxy')
};

ref = require('omega-target');
for (name in ref) {
  value = ref[name];
  if ((base = module.exports)[name] == null) {
    base[name] = value;
  }
}


},{"./external_api":11,"./inspect":14,"./options":15,"./proxy":16,"./storage":23,"./switchysharp":24,"./sync_storage":25,"./tabs":26,"./web_request_monitor":27,"omega-target":8,"url":7}],14:[function(require,module,exports){
var Inspect, OmegaPac, OmegaTarget, Promise,
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

module.exports = Inspect = (function() {
  Inspect.prototype._enabled = false;

  function Inspect(onInspect) {
    this.onInspect = onInspect;
  }

  Inspect.prototype.enable = function() {
    var webResource;
    if (chrome.contextMenus == null) {
      return;
    }
    if (chrome.i18n.getUILanguage == null) {
      return;
    }
    if (this._enabled) {
      return;
    }
    webResource = ["http://*/*", "https://*/*", "ftp://*/*"];

    /* Not so useful...
    chrome.contextMenus.create({
      id: 'inspectPage'
      title: chrome.i18n.getMessage('contextMenu_inspectPage')
      contexts: ['page']
      onclick: @inspect.bind(this)
      documentUrlPatterns: webResource
    })
     */
    chrome.contextMenus.create({
      id: 'inspectFrame',
      title: chrome.i18n.getMessage('contextMenu_inspectFrame'),
      contexts: ['frame'],
      documentUrlPatterns: webResource
    });
    chrome.contextMenus.create({
      id: 'inspectLink',
      title: chrome.i18n.getMessage('contextMenu_inspectLink'),
      contexts: ['link'],
      targetUrlPatterns: webResource
    });
    chrome.contextMenus.create({
      id: 'inspectElement',
      title: chrome.i18n.getMessage('contextMenu_inspectElement'),
      contexts: ['image', 'video', 'audio'],
      targetUrlPatterns: webResource
    });
    return this._enabled = true;
  };

  Inspect.prototype.disable = function() {
    var menuId, ref;
    if (!this._enabled) {
      return;
    }
    ref = this.propForMenuItem;
    for (menuId in ref) {
      if (!hasProp.call(ref, menuId)) continue;
      try {
        chrome.contextMenus.remove(menuId);
      } catch (error) {}
    }
    return this._enabled = false;
  };

  Inspect.prototype.propForMenuItem = {
    'inspectFrame': 'frameUrl',
    'inspectLink': 'linkUrl',
    'inspectElement': 'srcUrl'
  };

  Inspect.prototype.inspect = function(info, tab) {
    var url;
    if (!info.menuItemId) {
      return;
    }
    url = info[this.propForMenuItem[info.menuItemId]];
    if (!url && info.menuItemId === 'inspectPage') {
      url = tab.url;
    }
    if (!url) {
      return;
    }
    return this.onInspect(url, tab);
  };

  return Inspect;

})();


},{"omega-target":8}],15:[function(require,module,exports){
var ChromeOptions, ChromePort, OmegaPac, OmegaTarget, Promise, TEMPPROFILEKEY, Url, WebRequestMonitor, fetchUrl, querystring,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty,
  slice = [].slice;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

querystring = require('querystring');

WebRequestMonitor = require('./web_request_monitor');

ChromePort = require('./chrome_port');

fetchUrl = require('./fetch_url');

Url = require('url');

TEMPPROFILEKEY = 'tempProfileState';

ChromeOptions = (function(superClass) {
  extend(ChromeOptions, superClass);

  ChromeOptions.prototype._inspect = null;

  ChromeOptions.prototype._actionForUrl = null;

  ChromeOptions.prototype.fetchUrl = fetchUrl;

  ChromeOptions.prototype._networkInspectPorts = [];

  function ChromeOptions() {
    var args, ref;
    args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
    ChromeOptions.__super__.constructor.apply(this, args);
    chrome.runtime.onConnect.addListener((function(_this) {
      return function(port) {
        var disConnect, onMessage, originalEnabled;
        if (port.name !== 'network-inspect') {
          return;
        }
        if (!_this._requestMonitor) {
          originalEnabled = _this._monitorWebRequests;
          _this.setMonitorWebRequests(true);
          _this._monitorWebRequests = originalEnabled;
        }
        port.postMessage({
          type: 'connected'
        });
        onMessage = function(msg) {
          var _tabInfo, requestTabId;
          switch (msg.type) {
            case 'init':
              requestTabId = msg.tabId;
              _tabInfo = _this._requestMonitor.tabInfo;
              if (requestTabId) {
                _tabInfo = {};
                _tabInfo[requestTabId] = _this._requestMonitor.tabInfo[requestTabId];
              }
              return port.postMessage({
                type: 'init',
                data: _tabInfo
              });
          }
        };
        port.onMessage.addListener(onMessage);
        _this._networkInspectPorts.push(port);
        disConnect = function() {
          var pos, results1;
          port.onMessage.removeListener(onMessage);
          port.onDisconnect.removeListener(disConnect);
          pos = 0;
          results1 = [];
          while (pos >= 0) {
            pos = _this._networkInspectPorts.indexOf(port);
            if (pos >= 0) {
              results1.push(_this._networkInspectPorts.splice(pos, 1));
            } else {
              results1.push(void 0);
            }
          }
          return results1;
        };
        return port.onDisconnect.addListener(disConnect);
      };
    })(this));
    chrome.alarms.onAlarm.addListener((function(_this) {
      return function(alarm) {
        switch (alarm.name) {
          case 'omega.updateProfile':
            return _this.ready.then(function() {
              return _this.updateProfile();
            });
        }
      };
    })(this));
    if ((ref = chrome.contextMenus) != null) {
      ref.onClicked.addListener((function(_this) {
        return function(info, tab) {
          return _this.ready.then(function() {
            var changes, setOptions;
            switch (info.menuItemId) {
              case 'enableQuickSwitch':
                changes = {};
                changes['-enableQuickSwitch'] = info.checked;
                setOptions = _this._setOptions(changes);
                if (info.checked && !_this._quickSwitchCanEnable) {
                  return setOptions.then(function() {
                    return chrome.tabs.create({
                      url: chrome.runtime.getURL('options.html#/ui')
                    });
                  });
                }
            }
          });
        };
      })(this));
    }
    chrome.action.onClicked.addListener((function(_this) {
      return function(tab) {
        var ref1;
        if ((typeof browser !== "undefined" && browser !== null ? (ref1 = browser.proxy) != null ? ref1.onRequest : void 0 : void 0) != null) {
          browser.permissions.request({
            origins: ["<all_urls>"]
          });
        }
        return _this.ready.then(function() {
          var index, profiles;
          _this.clearBadge();
          if (!_this._options['-enableQuickSwitch']) {
            chrome.tabs.create({
              url: 'popup/index.html'
            });
            return;
          }
          profiles = _this._options['-quickSwitchProfiles'];
          index = profiles.indexOf(_this._currentProfileName);
          index = (index + 1) % profiles.length;
          return _this.applyProfile(profiles[index]).then(function() {
            var url;
            if (_this._options['-refreshOnProfileChange']) {
              url = tab.pendingUrl || tab.url;
              if (!url) {
                return;
              }
              if (url.substr(0, 6) === 'chrome') {
                return;
              }
              if (url.substr(0, 6) === 'about:') {
                return;
              }
              if (url.substr(0, 4) === 'moz-') {
                return;
              }
              if (tab.pendingUrl) {
                return chrome.tabs.update(tab.id, {
                  url: url
                });
              } else {
                return chrome.tabs.reload(tab.id);
              }
            }
          });
        });
      };
    })(this));
  }

  ChromeOptions.prototype.init = function(startupCheck) {
    ChromeOptions.__super__.init.call(this, startupCheck);
    this.ready.then((function(_this) {
      return function() {
        return chrome.storage.session.get(TEMPPROFILEKEY).then(function(tempProfileState) {
          if (tempProfileState == null) {
            tempProfileState = {};
          }
          tempProfileState = tempProfileState[TEMPPROFILEKEY];
          if (tempProfileState) {
            _this._tempProfile = tempProfileState._tempProfile;
            _this._tempProfile.rules.forEach(function(_rule) {
              var condition, domain, key;
              key = OmegaPac.Profiles.nameAsKey(_rule.profileName);
              _this._tempProfileRulesByProfile[key] = _this._tempProfileRulesByProfile[key] || [];
              _this._tempProfileRulesByProfile[key].push(_rule);
              condition = _rule.condition;
              domain = condition.pattern.substring(2);
              return _this._tempProfileRules[domain] = _rule;
            });
            _this._tempProfileActive = tempProfileState._tempProfileActive;
            OmegaPac.Profiles.updateRevision(_this._tempProfile);
            console.log('apply temp state profile', _this._currentProfileName);
            return _this.applyProfile(_this._currentProfileName);
          }
        });
      };
    })(this));
    return this.ready;
  };

  ChromeOptions.prototype.addTempRule = function(domain, profileName, toggle) {
    return ChromeOptions.__super__.addTempRule.call(this, domain, profileName, toggle).then((function(_this) {
      return function() {
        var _zeroState;
        _zeroState = {};
        _zeroState[TEMPPROFILEKEY] = {
          _tempProfile: _this._tempProfile,
          _tempProfileActive: _this._tempProfileActive
        };
        return chrome.storage.session.set(_zeroState);
      };
    })(this));
  };

  ChromeOptions.prototype.updateProfile = function() {
    var args;
    args = 1 <= arguments.length ? slice.call(arguments, 0) : [];
    return ChromeOptions.__super__.updateProfile.apply(this, args).then(function(results) {
      var error, profileName, result;
      error = false;
      for (profileName in results) {
        if (!hasProp.call(results, profileName)) continue;
        result = results[profileName];
        if (result instanceof Error) {
          error = true;
          break;
        }
      }
      if (error) {

        /*
        @setBadge(
          text: '!'
          color: '#faa732'
          title: chrome.i18n.getMessage('browserAction_titleDownloadFail')
        )
         */
      }
      return results;
    });
  };

  ChromeOptions.prototype._proxyNotControllable = null;

  ChromeOptions.prototype.proxyNotControllable = function() {
    return this._proxyNotControllable;
  };

  ChromeOptions.prototype.setProxyNotControllable = function(reason, badge) {
    this._proxyNotControllable = reason;
    if (reason) {
      this._state.set({
        'proxyNotControllable': reason
      });
      return this.setBadge(badge);
    } else {
      this._state.remove(['proxyNotControllable']);
      return this.clearBadge();
    }
  };

  ChromeOptions.prototype._badgeTitle = null;

  ChromeOptions.prototype.setBadge = function(options) {
    if (!options) {
      options = this._proxyNotControllable ? {
        text: '=',
        color: '#da4f49'
      } : {
        text: '?',
        color: '#49afcd'
      };
    }
    chrome.action.setBadgeText({
      text: options.text
    });
    chrome.action.setBadgeBackgroundColor({
      color: options.color
    });
    if (options.title) {
      this._badgeTitle = options.title;
      return chrome.action.setTitle({
        title: options.title
      });
    } else {
      return this._badgeTitle = null;
    }
  };

  ChromeOptions.prototype.clearBadge = function() {
    var base;
    if (this.externalApi.disabled) {
      return;
    }
    if (this._badgeTitle) {
      this.currentProfileChanged('clearBadge');
    }
    if (this._proxyNotControllable) {
      this.setBadge();
    } else {
      if (typeof (base = chrome.action).setBadgeText === "function") {
        base.setBadgeText({
          text: ''
        });
      }
    }
  };

  ChromeOptions.prototype._quickSwitchCanEnable = false;

  ChromeOptions.prototype.setQuickSwitch = function(quickSwitch, canEnable) {
    var ref;
    this._quickSwitchCanEnable = canEnable;
    if (quickSwitch) {
      chrome.action.setPopup({
        popup: ''
      });
    } else {
      chrome.action.setPopup({
        popup: globalThis.POPUPHTMLURL
      });
    }
    if ((ref = chrome.contextMenus) != null) {
      ref.update('enableQuickSwitch', {
        checked: !!quickSwitch
      });
    }
    return Promise.resolve();
  };

  ChromeOptions.prototype.setInspect = function(settings) {
    if (this._inspect) {
      if (settings.showMenu) {
        this._inspect.enable();
      } else {
        this._inspect.disable();
      }
    }
    return Promise.resolve();
  };

  ChromeOptions.prototype._requestMonitor = null;

  ChromeOptions.prototype._monitorWebRequests = false;

  ChromeOptions.prototype._tabRequestInfoPorts = null;

  ChromeOptions.prototype.setMonitorWebRequests = function(enabled) {
    var wildcardForReq;
    this._monitorWebRequests = enabled;
    if (enabled && (this._requestMonitor == null)) {
      this._tabRequestInfoPorts = {};
      wildcardForReq = function(req) {
        return OmegaPac.wildcardForUrl(req.url);
      };
      this._requestMonitor = new WebRequestMonitor(wildcardForReq);
      this._requestMonitor.watchTabs((function(_this) {
        return function(tabId, info, req, status) {
          var badge, ref, updateMessage;
          if (!/^(chrome|moz)-extension:\/\//i.test(req != null ? req.url : void 0)) {
            updateMessage = function() {
              return _this._networkInspectPorts.forEach(function(port) {
                return port.postMessage({
                  type: 'update',
                  data: {
                    tabId: tabId,
                    info: info,
                    req: req,
                    status: status
                  }
                });
              });
            };
            if (status === 'start') {
              _this._actionForUrl(req.url, {
                skipIcon: true
              }).then(function(action) {
                var request;
                request = info.requests[req.requestId];
                if (request) {
                  request.actionProfile = action;
                  return updateMessage();
                }
              });
            }
            updateMessage();
          }
          if (!_this._monitorWebRequests) {
            return;
          }
          if (info.errorCount > 0) {
            info.badgeSet = true;
            badge = {
              text: info.errorCount.toString(),
              color: '#f0ad4e'
            };
            chrome.action.setBadgeText({
              text: badge.text,
              tabId: tabId
            })["catch"](function(e) {
              return console.log('error:', e);
            });
            chrome.action.setBadgeBackgroundColor({
              color: badge.color,
              tabId: tabId
            })["catch"](function(e) {
              return console.log('error:', e);
            });
          } else if (info.badgeSet) {
            info.badgeSet = false;
            chrome.action.setBadgeText({
              text: '',
              tabId: tabId
            })["catch"](function(e) {
              return console.log('error:', e);
            });
          }
          return (ref = _this._tabRequestInfoPorts[tabId]) != null ? ref.postMessage({
            errorCount: info.errorCount,
            summary: info.summary
          }) : void 0;
        };
      })(this));
      return chrome.runtime.onConnect.addListener((function(_this) {
        return function(rawPort) {
          var port, tabId;
          if (rawPort.name !== 'tabRequestInfo') {
            return;
          }
          if (!_this._monitorWebRequests) {
            return;
          }
          tabId = null;
          port = new ChromePort(rawPort);
          port.onMessage.addListener(function(msg) {
            var info;
            tabId = msg.tabId;
            _this._tabRequestInfoPorts[tabId] = port;
            info = _this._requestMonitor.tabInfo[tabId];
            if (info) {
              return port.postMessage({
                errorCount: info.errorCount,
                summary: info.summary
              });
            }
          });
          return port.onDisconnect.addListener(function() {
            if (tabId != null) {
              return delete _this._tabRequestInfoPorts[tabId];
            }
          });
        };
      })(this));
    }
  };

  ChromeOptions.prototype.schedule = function(name, periodInMinutes) {
    name = 'omega.' + name;
    if (periodInMinutes < 0) {
      chrome.alarms.clear(name);
    } else {
      chrome.alarms.create(name, {
        periodInMinutes: periodInMinutes
      });
    }
    return Promise.resolve();
  };

  ChromeOptions.prototype.printFixedProfile = function(profile) {
    var i, len, pacResult, ref, result, scheme;
    if (profile.profileType !== 'FixedProfile') {
      return;
    }
    result = '';
    ref = OmegaPac.Profiles.schemes;
    for (i = 0, len = ref.length; i < len; i++) {
      scheme = ref[i];
      if (!profile[scheme.prop]) {
        continue;
      }
      pacResult = OmegaPac.Profiles.pacResult(profile[scheme.prop]);
      if (scheme.scheme) {
        result += scheme.scheme + ": " + pacResult + "\n";
      } else {
        result += pacResult + "\n";
      }
    }
    result || (result = chrome.i18n.getMessage('browserAction_profileDetails_DirectProfile'));
    return result;
  };

  ChromeOptions.prototype.printProfile = function(profile) {
    var type;
    type = profile.profileType;
    if (type.indexOf('RuleListProfile') >= 0) {
      type = 'RuleListProfile';
    }
    if (type === 'FixedProfile') {
      return this.printFixedProfile(profile);
    } else if (type === 'PacProfile' && profile.pacUrl) {
      return profile.pacUrl;
    } else {
      return chrome.i18n.getMessage('browserAction_profileDetails_' + type) || null;
    }
  };

  ChromeOptions.prototype.upgrade = function(options, changes) {
    return ChromeOptions.__super__.upgrade.call(this, options)["catch"](function(err) {
      if (options != null ? options['schemaVersion'] : void 0) {
        return Promise.reject(err);
      }
      return Promise.reject(new OmegaTarget.Options.NoOptionsError());
    });
  };

  ChromeOptions.prototype.onFirstRun = function(reason) {
    console.log('first run disabled by 小喵');
    // 🚫 小喵修改：禁用首次运行时自动打开选项页面
    return Promise.resolve();
  };

  ChromeOptions.prototype.getPageInfo = function(arg) {
    var errorCount, getBadge, getInspectUrl, ref, ref1, result, tabId, url;
    tabId = arg.tabId, url = arg.url;
    errorCount = (ref = this._requestMonitor) != null ? (ref1 = ref.tabInfo[tabId]) != null ? ref1.errorCount : void 0 : void 0;
    result = errorCount ? {
      errorCount: errorCount
    } : null;
    getBadge = new Promise(function(resolve, reject) {
      if (chrome.action.getBadgeText == null) {
        resolve('');
        return;
      }
      return chrome.action.getBadgeText({
        tabId: tabId
      }, function(result) {
        return resolve(result);
      });
    });
    getInspectUrl = this._state.get({
      inspectUrl: ''
    });
    return Promise.join(getBadge, getInspectUrl, (function(_this) {
      return function(badge, arg1) {
        var domain, errorPagePrefix, inspectUrl, subdomain;
        inspectUrl = arg1.inspectUrl;
        if (badge === '#' && inspectUrl) {
          url = inspectUrl;
        } else {
          _this.clearBadge();
        }
        if (!url) {
          return result;
        }
        if (url.substr(0, 6) === 'chrome') {
          errorPagePrefix = 'chrome://errorpage/';
          if (url.substr(0, errorPagePrefix.length) === errorPagePrefix) {
            url = querystring.parse(url.substr(url.indexOf('?') + 1)).lasturl;
            if (!url) {
              return result;
            }
          } else {
            return result;
          }
        }
        if (url.substr(0, 6) === 'about:') {
          return result;
        }
        if (url.substr(0, 4) === 'moz-') {
          return result;
        }
        domain = OmegaPac.getBaseDomain(Url.parse(url).hostname);
        subdomain = OmegaPac.getSubdomain(url);
        return {
          url: url,
          domain: domain,
          subdomain: subdomain,
          tempRuleProfileName: _this.queryTempRule(domain),
          errorCount: errorCount
        };
      };
    })(this));
  };

  return ChromeOptions;

})(OmegaTarget.Options);

module.exports = ChromeOptions;


},{"./chrome_port":10,"./fetch_url":12,"./web_request_monitor":27,"omega-target":8,"querystring":6,"url":7}],16:[function(require,module,exports){
var FirefoxProxyImpl, ListenerProxyImpl, ScriptProxyImpl, SettingsProxyImpl;

FirefoxProxyImpl = require('./proxy_impl_firefox');

ListenerProxyImpl = require('./proxy_impl_listener');

SettingsProxyImpl = require('./proxy_impl_settings');

ScriptProxyImpl = require('./proxy_impl_script');

exports.proxyImpls = [FirefoxProxyImpl, ListenerProxyImpl, ScriptProxyImpl, SettingsProxyImpl];

exports.getProxyImpl = function(log) {
  var Impl, i, len, ref;
  ref = exports.proxyImpls;
  for (i = 0, len = ref.length; i < len; i++) {
    Impl = ref[i];
    if (Impl.isSupported()) {
      return new Impl(log);
    }
  }
  throw new Error('Your browser does not support proxy settings!');
};


},{"./proxy_impl_firefox":19,"./proxy_impl_listener":20,"./proxy_impl_script":21,"./proxy_impl_settings":22}],17:[function(require,module,exports){
var OmegaPac, OmegaTarget, Promise, ProxyAuth;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

module.exports = ProxyAuth = (function() {
  function ProxyAuth(log) {
    this._requests = {};
    this.log = log;
  }

  ProxyAuth.prototype.listening = false;

  ProxyAuth.prototype.listen = function() {
    if (this.listening) {
      return;
    }
    if (!chrome.webRequest) {
      this.log.error('Proxy auth disabled! No webRequest permission.');
      return;
    }
    if (!chrome.webRequest.onAuthRequired) {
      this.log.error('Proxy auth disabled! onAuthRequired not available.');
      return;
    }
    chrome.webRequest.onAuthRequired.addListener(this.authHandler.bind(this), {
      urls: ['<all_urls>']
    }, ['blocking']);
    chrome.webRequest.onCompleted.addListener(this._requestDone.bind(this), {
      urls: ['<all_urls>']
    });
    chrome.webRequest.onErrorOccurred.addListener(this._requestDone.bind(this), {
      urls: ['<all_urls>']
    });
    return this.listening = true;
  };

  ProxyAuth.prototype._keyForProxy = function(proxy) {
    return (proxy.host.toLowerCase()) + ":" + proxy.port;
  };

  ProxyAuth.prototype.setProxies = function(profiles) {
    var auth, fallback, i, j, key, len, len1, list, profile, proxy, ref, ref1, ref2, results, scheme;
    this._proxies = {};
    this._fallbacks = [];
    results = [];
    for (i = 0, len = profiles.length; i < len; i++) {
      profile = profiles[i];
      if (!profile.auth) {
        continue;
      }
      ref = OmegaPac.Profiles.schemes;
      for (j = 0, len1 = ref.length; j < len1; j++) {
        scheme = ref[j];
        if (!profile[scheme.prop]) {
          continue;
        }
        auth = (ref1 = profile.auth) != null ? ref1[scheme.prop] : void 0;
        if (!auth) {
          continue;
        }
        proxy = profile[scheme.prop];
        key = this._keyForProxy(proxy);
        list = this._proxies[key];
        if (list == null) {
          this._proxies[key] = list = [];
        }
        list.push({
          config: proxy,
          auth: auth,
          name: profile.name + '.' + scheme.prop
        });
      }
      fallback = (ref2 = profile.auth) != null ? ref2['all'] : void 0;
      if (fallback != null) {
        results.push(this._fallbacks.push({
          auth: fallback,
          name: profile.name + '.' + 'all'
        }));
      } else {
        results.push(void 0);
      }
    }
    return results;
  };

  ProxyAuth.prototype._proxies = {};

  ProxyAuth.prototype._fallbacks = [];

  ProxyAuth.prototype._requests = null;

  ProxyAuth.prototype.authHandler = function(details) {
    var key, list, listLen, proxy, req;
    if (!details.isProxy) {
      return {};
    }
    req = this._requests[details.requestId];
    if (req == null) {
      this._requests[details.requestId] = req = {
        authTries: 0
      };
    }
    key = this._keyForProxy({
      host: details.challenger.host,
      port: details.challenger.port
    });
    list = this._proxies[key];
    listLen = list != null ? list.length : 0;
    if (req.authTries < listLen) {
      proxy = list[req.authTries];
    } else {
      proxy = this._fallbacks[req.authTries - listLen];
    }
    this.log.log('ProxyAuth', key, req.authTries, proxy != null ? proxy.name : void 0);
    if (proxy == null) {
      return {};
    }
    req.authTries++;
    return {
      authCredentials: proxy.auth
    };
  };

  ProxyAuth.prototype._requestDone = function(details) {
    return delete this._requests[details.requestId];
  };

  return ProxyAuth;

})();


},{"omega-target":8}],18:[function(require,module,exports){
var OmegaTarget, Promise, ProxyAuth, ProxyImpl, profilePacCache,
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

Promise = OmegaTarget.Promise;

ProxyAuth = require('./proxy_auth');

profilePacCache = new Map();

ProxyImpl = (function() {
  function ProxyImpl(log) {
    this.log = log;
  }

  ProxyImpl.isSupported = function() {
    return false;
  };

  ProxyImpl.prototype.applyProfile = function(profile, meta) {
    return Promise.reject();
  };

  ProxyImpl.prototype.watchProxyChange = function(callback) {
    return null;
  };

  ProxyImpl.prototype.parseExternalProfile = function(details, options) {
    return null;
  };

  ProxyImpl.prototype._profileNotFound = function(name) {
    this.log.error("Profile " + name + " not found! Things may go very, very wrong.");
    return OmegaPac.Profiles.create({
      name: name,
      profileType: 'VirtualProfile',
      defaultProfileName: 'direct'
    });
  };

  ProxyImpl.prototype.setProxyAuth = function(profile, options) {
    return Promise["try"]((function(_this) {
      return function() {
        var _, name, ref_set, referenced_profiles;
        if (_this._proxyAuth == null) {
          _this._proxyAuth = new ProxyAuth(_this.log);
        }
        _this._proxyAuth.listen();
        referenced_profiles = [];
        ref_set = OmegaPac.Profiles.allReferenceSet(profile, options, {
          profileNotFound: _this._profileNotFound.bind(_this)
        });
        for (_ in ref_set) {
          if (!hasProp.call(ref_set, _)) continue;
          name = ref_set[_];
          profile = OmegaPac.Profiles.byName(name, options);
          if (profile) {
            referenced_profiles.push(profile);
          }
        }
        return _this._proxyAuth.setProxies(referenced_profiles);
      };
    })(this));
  };

  ProxyImpl.prototype.getProfilePacScript = function(profile, meta, options) {
    var _, _profile, allProfiles, ast, cachedProfiles, name, pacScript, prefix, profileName, profilePac, profilePacKey, ref_set, referenced_profiles, script;
    if (meta == null) {
      meta = profile;
    }
    referenced_profiles = [];
    ref_set = OmegaPac.Profiles.allReferenceSet(profile, options, {
      profileNotFound: this._profileNotFound.bind(this)
    });
    for (_ in ref_set) {
      if (!hasProp.call(ref_set, _)) continue;
      name = ref_set[_];
      _profile = OmegaPac.Profiles.byName(name, options);
      if (_profile) {
        referenced_profiles.push(_profile);
      }
    }
    cachedProfiles = Array.from(profilePacCache.keys());
    allProfiles = Object.values(options);
    cachedProfiles.forEach(function(cachedProfile) {
      if (allProfiles.indexOf(cachedProfile) < 0) {
        return profilePacCache["delete"](cachedProfile);
      }
    });
    profilePac = profilePacCache.get(profile);
    profilePacKey = referenced_profiles.map(function(_profile) {
      var revision;
      revision = _profile.revision || 1;
      if (OmegaPac.Profiles.updateUrl(_profile) && _profile.sha256) {
        revision = _profile.sha256;
      }
      return _profile.name + '_' + revision;
    }).join(',');
    if (profilePac != null ? profilePac[profilePacKey] : void 0) {
      return profilePac[profilePacKey];
    }
    ast = OmegaPac.PacGenerator.script(options, profile, {
      profileNotFound: this._profileNotFound.bind(this)
    });
    ast = OmegaPac.PacGenerator.compress(ast);
    script = OmegaPac.PacGenerator.ascii(ast.print_to_string());
    profileName = OmegaPac.PacGenerator.ascii(JSON.stringify(meta.name));
    profileName = profileName.replace(/\*/g, '\\u002a');
    profileName = profileName.replace(/\\/g, '\\u002f');
    prefix = "/*OmegaProfile*" + profileName + "*" + meta.revision + "*/";
    pacScript = prefix + script;
    profilePac = {};
    profilePac[profilePacKey] = pacScript;
    profilePacCache.set(profile, profilePac);
    return pacScript;
  };

  return ProxyImpl;

})();

module.exports = ProxyImpl;


},{"./proxy_auth":17,"omega-target":8}],19:[function(require,module,exports){
var FirefoxProxyImpl, NativePromise, OmegaTarget, ProxyImpl, blobUrl,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

NativePromise = typeof Promise !== "undefined" && Promise !== null ? Promise : null;

ProxyImpl = require('./proxy_impl');

blobUrl = null;

FirefoxProxyImpl = (function(superClass) {
  extend(FirefoxProxyImpl, superClass);

  FirefoxProxyImpl.isSupported = function() {
    var ref, ref1;
    return !!chrome.contextMenus && (typeof Promise !== "undefined" && Promise !== null) && ((typeof browser !== "undefined" && browser !== null ? (ref = browser.proxy) != null ? ref.onRequest : void 0 : void 0) != null) && (typeof browser !== "undefined" && browser !== null ? (ref1 = browser.proxy) != null ? ref1.settings : void 0 : void 0);
  };

  FirefoxProxyImpl.prototype.features = ['fullUrl', 'socks5Auth'];

  function FirefoxProxyImpl() {
    FirefoxProxyImpl.__super__.constructor.apply(this, arguments);
    this._optionsReady = new NativePromise((function(_this) {
      return function(resolve) {
        return _this._optionsReadyCallback = resolve;
      };
    })(this));
    this._initRequestListeners();
  }

  FirefoxProxyImpl.prototype._initRequestListeners = function() {
    browser.proxy.onRequest.addListener(this.onRequest.bind(this), {
      urls: ["<all_urls>"]
    });
    return browser.proxy.onError.addListener(this.onError.bind(this));
  };

  FirefoxProxyImpl.prototype.watchProxyChange = function(callback) {
    return null;
  };

  FirefoxProxyImpl.prototype.applyProfile = function(profile, state, options) {
    var blob, pacScript;
    if (blobUrl) {
      URL.revokeObjectURL(blobUrl);
    }
    if (browser.extension.isAllowedIncognitoAccess()) {
      if (profile.profileType === 'DirectProfile') {
        browser.proxy.settings.set({
          value: {
            proxyType: 'none'
          }
        });
      } else if (profile.profileType === 'SystemProfile') {
        browser.proxy.settings.clear({});
      } else {
        pacScript = this.getProfilePacScript(profile, state, options);
        blob = new Blob([pacScript], {
          type: 'application/x-ns-proxy-autoconfig'
        });
        blobUrl = URL.createObjectURL(blob);
        browser.proxy.settings.set({
          value: {
            proxyDNS: true,
            proxyType: 'autoConfig',
            autoConfigUrl: blobUrl
          }
        });
      }
    }
    this._options = options;
    this._profile = profile;
    if (typeof this._optionsReadyCallback === "function") {
      this._optionsReadyCallback();
    }
    this._optionsReadyCallback = null;
    return this.setProxyAuth(profile, options);
  };

  FirefoxProxyImpl.prototype.onRequest = function(requestDetails) {
    return NativePromise.resolve(this._optionsReady.then((function(_this) {
      return function() {
        var auth, next, profile, proxy, request, result;
        request = OmegaPac.Conditions.requestFromUrl(requestDetails.url);
        profile = _this._profile;
        while (profile) {
          result = OmegaPac.Profiles.match(profile, request);
          if (!result) {
            switch (profile.profileType) {
              case 'DirectProfile':
                return {
                  type: 'direct'
                };
              case 'SystemProfile':
              case 'PacProfile':
                return void 0;
              default:
                throw new Error('Unsupported profile: ' + profile.profileType);
            }
          }
          if (Array.isArray(result)) {
            proxy = result[2];
            auth = result[3];
            if (proxy) {
              return _this.proxyInfo(proxy, auth);
            }
            next = result[0];
          } else if (result.profileName) {
            next = OmegaPac.Profiles.nameAsKey(result.profileName);
          } else {
            break;
          }
          profile = OmegaPac.Profiles.byKey(next, _this._options);
        }
        throw new Error('Profile not found: ' + next);
      };
    })(this)));
  };

  FirefoxProxyImpl.prototype.onError = function(error) {
    return this.log.error(error);
  };

  FirefoxProxyImpl.prototype.proxyInfo = function(proxy, auth) {
    var proxyInfo;
    proxyInfo = {
      type: proxy.scheme,
      host: proxy.host,
      port: proxy.port
    };
    if (proxyInfo.type === 'socks5') {
      proxyInfo.type = 'socks';
      if (auth) {
        proxyInfo.username = auth.username;
        proxyInfo.password = auth.password;
      }
    }
    if (proxyInfo.type === 'socks') {
      proxyInfo.proxyDNS = true;
    }
    return [proxyInfo];
  };

  return FirefoxProxyImpl;

})(ProxyImpl);

module.exports = FirefoxProxyImpl;


},{"./proxy_impl":18,"omega-target":8}],20:[function(require,module,exports){
var ListenerProxyImpl, NativePromise, OmegaTarget, ProxyImpl,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

NativePromise = typeof Promise !== "undefined" && Promise !== null ? Promise : null;

ProxyImpl = require('./proxy_impl');

ListenerProxyImpl = (function(superClass) {
  extend(ListenerProxyImpl, superClass);

  ListenerProxyImpl.isSupported = function() {
    var ref;
    return (typeof Promise !== "undefined" && Promise !== null) && ((typeof browser !== "undefined" && browser !== null ? (ref = browser.proxy) != null ? ref.onRequest : void 0 : void 0) != null);
  };

  ListenerProxyImpl.prototype.features = ['fullUrl', 'socks5Auth'];

  function ListenerProxyImpl() {
    ListenerProxyImpl.__super__.constructor.apply(this, arguments);
    this._optionsReady = new NativePromise((function(_this) {
      return function(resolve) {
        return _this._optionsReadyCallback = resolve;
      };
    })(this));
    this._initRequestListeners();
  }

  ListenerProxyImpl.prototype._initRequestListeners = function() {
    browser.proxy.onRequest.addListener(this.onRequest.bind(this), {
      urls: ["<all_urls>"]
    });
    return browser.proxy.onError.addListener(this.onError.bind(this));
  };

  ListenerProxyImpl.prototype.watchProxyChange = function(callback) {
    return null;
  };

  ListenerProxyImpl.prototype.applyProfile = function(profile, state, options) {
    this._options = options;
    this._profile = profile;
    if (typeof this._optionsReadyCallback === "function") {
      this._optionsReadyCallback();
    }
    this._optionsReadyCallback = null;
    return this.setProxyAuth(profile, options);
  };

  ListenerProxyImpl.prototype.onRequest = function(requestDetails) {
    return NativePromise.resolve(this._optionsReady.then((function(_this) {
      return function() {
        var auth, next, profile, proxy, request, result;
        request = OmegaPac.Conditions.requestFromUrl(requestDetails.url);
        profile = _this._profile;
        while (profile) {
          result = OmegaPac.Profiles.match(profile, request);
          if (!result) {
            switch (profile.profileType) {
              case 'DirectProfile':
                return {
                  type: 'direct'
                };
              case 'SystemProfile':
                return void 0;
              default:
                throw new Error('Unsupported profile: ' + profile.profileType);
            }
          }
          if (Array.isArray(result)) {
            proxy = result[2];
            auth = result[3];
            if (proxy) {
              return _this.proxyInfo(proxy, auth);
            }
            next = result[0];
          } else if (result.profileName) {
            next = OmegaPac.Profiles.nameAsKey(result.profileName);
          } else {
            break;
          }
          profile = OmegaPac.Profiles.byKey(next, _this._options);
        }
        throw new Error('Profile not found: ' + next);
      };
    })(this)));
  };

  ListenerProxyImpl.prototype.onError = function(error) {
    return this.log.error(error);
  };

  ListenerProxyImpl.prototype.proxyInfo = function(proxy, auth) {
    var proxyInfo;
    proxyInfo = {
      type: proxy.scheme,
      host: proxy.host,
      port: proxy.port
    };
    if (proxyInfo.type === 'socks5') {
      proxyInfo.type = 'socks';
      if (auth) {
        proxyInfo.username = auth.username;
        proxyInfo.password = auth.password;
      }
    }
    if (proxyInfo.type === 'socks') {
      proxyInfo.proxyDNS = true;
    }
    return [proxyInfo];
  };

  return ListenerProxyImpl;

})(ProxyImpl);

module.exports = ListenerProxyImpl;


},{"./proxy_impl":18,"omega-target":8}],21:[function(require,module,exports){
var OmegaTarget, Promise, ProxyImpl, ScriptProxyImpl,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

Promise = OmegaTarget.Promise;

ProxyImpl = require('./proxy_impl');

ScriptProxyImpl = (function(superClass) {
  extend(ScriptProxyImpl, superClass);

  function ScriptProxyImpl() {
    return ScriptProxyImpl.__super__.constructor.apply(this, arguments);
  }

  ScriptProxyImpl.isSupported = function() {
    var ref, ref1;
    return ((typeof browser !== "undefined" && browser !== null ? (ref = browser.proxy) != null ? ref.register : void 0 : void 0) != null) || ((typeof browser !== "undefined" && browser !== null ? (ref1 = browser.proxy) != null ? ref1.registerProxyScript : void 0 : void 0) != null);
  };

  ScriptProxyImpl.prototype.features = ['socks5Auth'];

  ScriptProxyImpl.prototype._proxyScriptUrl = 'js/omega_webext_proxy_script.min.js';

  ScriptProxyImpl.prototype._proxyScriptDisabled = false;

  ScriptProxyImpl.prototype._proxyScriptInitialized = false;

  ScriptProxyImpl.prototype._proxyScriptState = {};

  ScriptProxyImpl.prototype.watchProxyChange = function(callback) {
    return null;
  };

  ScriptProxyImpl.prototype.applyProfile = function(profile, state, options) {
    this.log.error('Your browser is outdated! Full-URL based matching, etc. unsupported! ' + "Please update your browser ASAP!");
    state = state != null ? state : {};
    this._options = options;
    state.currentProfileName = profile.name;
    if (profile.name === '') {
      state.tempProfile = profile;
    }
    if (profile.profileType === 'SystemProfile') {
      if (browser.proxy.unregister != null) {
        browser.proxy.unregister();
      } else {
        browser.proxy.registerProxyScript('js/omega_invalid_proxy_script.js');
      }
      this._proxyScriptDisabled = true;
    } else {
      this._proxyScriptState = state;
      Promise.all([browser.runtime.getBrowserInfo(), this._initWebextProxyScript()]).then((function(_this) {
        return function(arg) {
          var info;
          info = arg[0];
          if (info.vendor === 'Mozilla' && info.buildID < '20170918220054') {
            _this.log.error('Your browser is outdated! SOCKS5 DNS/Auth unsupported! ' + ("Please update your browser ASAP! (Current Build " + info.buildID + ")"));
            _this._proxyScriptState.useLegacyStringReturn = true;
          }
          return _this._proxyScriptStateChanged();
        };
      })(this));
    }
    return this.setProxyAuth(profile, options);
  };

  ScriptProxyImpl.prototype._initWebextProxyScript = function() {
    var promise;
    if (!this._proxyScriptInitialized) {
      browser.proxy.onProxyError.addListener((function(_this) {
        return function(err) {
          if ((err != null ? err.message : void 0) != null) {
            if (err.message.indexOf('Invalid Proxy Rule: DIRECT') >= 0) {
              return;
            }
            if (err.message.indexOf('Return type must be a string') >= 0) {
              _this.log.error('Your browser is outdated! SOCKS5 DNS/Auth unsupported! ' + 'Please update your browser ASAP!');
              _this._proxyScriptState.useLegacyStringReturn = true;
              _this._proxyScriptStateChanged();
              return;
            }
          }
          return _this.log.error(err);
        };
      })(this));
      browser.runtime.onMessage.addListener((function(_this) {
        return function(message) {
          if (message.event !== 'proxyScriptLog') {
            return;
          }
          if (message.level === 'error') {
            return _this.log.error(message);
          } else if (message.level === 'warn') {
            return _this.log.error(message);
          } else {
            return _this.log.log(message);
          }
        };
      })(this));
    }
    if (!this._proxyScriptInitialized || this._proxyScriptDisabled) {
      promise = new Promise(function(resolve) {
        var onMessage;
        onMessage = function(message) {
          if (message.event !== 'proxyScriptLoaded') {
            return;
          }
          resolve();
          browser.runtime.onMessage.removeListener(onMessage);
        };
        return browser.runtime.onMessage.addListener(onMessage);
      });
      if (browser.proxy.register != null) {
        browser.proxy.register(this._proxyScriptUrl);
      } else {
        browser.proxy.registerProxyScript(this._proxyScriptUrl);
      }
      this._proxyScriptDisabled = false;
    } else {
      promise = Promise.resolve();
    }
    this._proxyScriptInitialized = true;
    return promise;
  };

  ScriptProxyImpl.prototype._proxyScriptStateChanged = function() {
    return browser.runtime.sendMessage({
      event: 'proxyScriptStateChanged',
      state: this._proxyScriptState,
      options: this._options
    }, {
      toProxyScript: true
    });
  };

  return ScriptProxyImpl;

})(ProxyImpl);

module.exports = ScriptProxyImpl;


},{"./proxy_impl":18,"omega-target":8}],22:[function(require,module,exports){
var OmegaPac, OmegaTarget, Promise, ProxyImpl, SettingsProxyImpl, addAuthPreflightRule, chromeApiPromisify, getFirstAuthProfile, notExistentWebsite,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

chromeApiPromisify = require('../chrome_api').chromeApiPromisify;

ProxyImpl = require('./proxy_impl');

notExistentWebsite = "preflight-auth.non-existent-website.zzzzzzzzeroomega.zero";

getFirstAuthProfile = function(profile, options) {
  var _profile, j, len, profileName, ref, rule;
  ref = profile.rules;
  for (j = 0, len = ref.length; j < len; j++) {
    rule = ref[j];
    profileName = rule.profileName;
    _profile = OmegaPac.Profiles.byName(profileName, options);
    if (_profile != null ? _profile.auth : void 0) {
      return _profile;
    }
  }
};

addAuthPreflightRule = function(profile, options) {
  var authProfile;
  if (profile.profileType !== 'SwitchProfile') {
    return;
  }
  if (!(profile.rules || profile.rules.length === 0)) {
    return;
  }
  authProfile = getFirstAuthProfile(profile, options);
  if (!authProfile) {
    return;
  }
  profile.rules.unshift({
    "condition": {
      "conditionType": "HostWildcardCondition",
      "pattern": notExistentWebsite
    },
    "isPreflightRule": true,
    "profileName": authProfile.name
  });
  return authProfile;
};

SettingsProxyImpl = (function(superClass) {
  extend(SettingsProxyImpl, superClass);

  function SettingsProxyImpl() {
    return SettingsProxyImpl.__super__.constructor.apply(this, arguments);
  }

  SettingsProxyImpl.isSupported = function() {
    var ref;
    return (typeof chrome !== "undefined" && chrome !== null ? (ref = chrome.proxy) != null ? ref.settings : void 0 : void 0) != null;
  };

  SettingsProxyImpl.prototype.features = ['fullUrlHttp', 'pacScript', 'watchProxyChange'];

  SettingsProxyImpl.prototype.applyProfile = function(profile, meta, options) {
    var authProfile, config;
    if (meta == null) {
      meta = profile;
    }
    if (profile.profileType === 'SystemProfile') {
      return chromeApiPromisify(chrome.proxy.settings, 'clear')({}).then((function(_this) {
        return function() {
          chrome.proxy.settings.get({}, _this._proxyChangeListener);
        };
      })(this));
    }
    authProfile = null;
    config = {};
    if (profile.profileType === 'DirectProfile') {
      config['mode'] = 'direct';
    } else if (profile.profileType === 'PacProfile') {
      config['mode'] = 'pac_script';
      config['pacScript'] = !profile.pacScript || OmegaPac.Profiles.isFileUrl(profile.pacUrl) ? {
        url: profile.pacUrl,
        mandatory: true
      } : {
        data: OmegaPac.PacGenerator.ascii(profile.pacScript),
        mandatory: true
      };
    } else if (profile.profileType === 'FixedProfile') {
      config = this._fixedProfileConfig(profile);
    } else {
      config['mode'] = 'pac_script';
      authProfile = addAuthPreflightRule(profile, options);
      config['pacScript'] = {
        mandatory: true,
        data: this.getProfilePacScript(profile, meta, options)
      };
    }
    return this.setProxyAuth(profile, options).then(function() {
      return chromeApiPromisify(chrome.proxy.settings, 'set')({
        value: config
      });
    }).then((function(_this) {
      return function() {
        if (authProfile) {
          fetch("https://" + notExistentWebsite)["catch"](function() {
            return authProfile;
          });
        }
        chrome.proxy.settings.get({}, _this._proxyChangeListener);
      };
    })(this))["finally"](function() {
      if (authProfile) {
        profile.rules.forEach(function(rule, index) {
          if (rule.isPreflightRule) {
            return profile.rules.splice(index, 1);
          }
        });
        return profile.rules;
      }
    });
  };

  SettingsProxyImpl.prototype._fixedProfileConfig = function(profile) {
    var bypassList, condition, config, j, k, l, len, len1, len2, protocol, protocolProxySet, protocols, ref, rules;
    config = {};
    config['mode'] = 'fixed_servers';
    rules = {};
    protocols = ['proxyForHttp', 'proxyForHttps', 'proxyForFtp'];
    protocolProxySet = false;
    for (j = 0, len = protocols.length; j < len; j++) {
      protocol = protocols[j];
      if (!(profile[protocol] != null)) {
        continue;
      }
      rules[protocol] = profile[protocol];
      protocolProxySet = true;
    }
    if (profile.fallbackProxy) {
      if (profile.fallbackProxy.scheme === 'http') {
        if (!protocolProxySet) {
          rules['singleProxy'] = profile.fallbackProxy;
        } else {
          for (k = 0, len1 = protocols.length; k < len1; k++) {
            protocol = protocols[k];
            if (rules[protocol] == null) {
              rules[protocol] = JSON.parse(JSON.stringify(profile.fallbackProxy));
            }
          }
        }
      } else {
        rules['fallbackProxy'] = profile.fallbackProxy;
      }
    } else if (!protocolProxySet) {
      config['mode'] = 'direct';
    }
    if (config['mode'] !== 'direct') {
      rules['bypassList'] = bypassList = [];
      ref = profile.bypassList;
      for (l = 0, len2 = ref.length; l < len2; l++) {
        condition = ref[l];
        bypassList.push(this._formatBypassItem(condition));
      }
      config['rules'] = rules;
    }
    return config;
  };

  SettingsProxyImpl.prototype._formatBypassItem = function(condition) {
    var i, str;
    str = OmegaPac.Conditions.str(condition);
    i = str.indexOf(' ');
    return str.substr(i + 1);
  };

  SettingsProxyImpl.prototype._proxyChangeWatchers = null;

  SettingsProxyImpl.prototype._proxyChangeListener = function(details) {
    var j, len, ref, ref1, results, watcher;
    ref1 = (ref = this._proxyChangeWatchers) != null ? ref : [];
    results = [];
    for (j = 0, len = ref1.length; j < len; j++) {
      watcher = ref1[j];
      results.push(watcher(details));
    }
    return results;
  };

  SettingsProxyImpl.prototype.watchProxyChange = function(callback) {
    var ref, ref1;
    if (this._proxyChangeWatchers == null) {
      this._proxyChangeWatchers = [];
      if ((typeof chrome !== "undefined" && chrome !== null ? (ref = chrome.proxy) != null ? (ref1 = ref.settings) != null ? ref1.onChange : void 0 : void 0 : void 0) != null) {
        chrome.proxy.settings.onChange.addListener(this._proxyChangeListener.bind(this));
      }
    }
    this._proxyChangeWatchers.push(callback);
  };

  SettingsProxyImpl.prototype.parseExternalProfile = function(details, options) {
    var bypassCount, bypassSet, host, j, k, l, len, len1, len2, len3, m, pattern, profile, prop, props, proxies, ref, ref1, result, url;
    if (details.name) {
      return details;
    }
    switch (details.value.mode) {
      case 'system':
        return OmegaPac.Profiles.byName('system');
      case 'direct':
        return OmegaPac.Profiles.byName('direct');
      case 'auto_detect':
        return OmegaPac.Profiles.create({
          profileType: 'PacProfile',
          name: '',
          pacUrl: 'http://wpad/wpad.dat'
        });
      case 'pac_script':
        url = details.value.pacScript.url;
        if (url) {
          profile = null;
          OmegaPac.Profiles.each(options, function(key, p) {
            if (p.profileType === 'PacProfile' && p.pacUrl === url) {
              return profile = p;
            }
          });
          return profile != null ? profile : OmegaPac.Profiles.create({
            profileType: 'PacProfile',
            name: '',
            pacUrl: url
          });
        } else {
          return (function() {
            var _, end, i, magic, profileName, revision, script, tokens;
            profile = null;
            script = details.value.pacScript.data;
            OmegaPac.Profiles.each(options, function(key, p) {
              if (p.profileType === 'PacProfile' && p.pacScript === script) {
                return profile = p;
              }
            });
            if (profile) {
              return profile;
            }
            script = script.trim();
            magic = '/*OmegaProfile*';
            if (script.substr(0, magic.length) === magic) {
              end = script.indexOf('*/');
              if (end > 0) {
                i = magic.length;
                tokens = script.substring(magic.length, end).split('*');
                profileName = tokens[0], revision = tokens[1];
                try {
                  profileName = JSON.parse(profileName);
                } catch (error) {
                  _ = error;
                  profileName = null;
                }
                if (profileName && revision) {
                  profile = OmegaPac.Profiles.byName(profileName, options);
                  if (OmegaPac.Revision.compare(profile.revision, revision) === 0) {
                    return profile;
                  }
                }
              }
            }
            return OmegaPac.Profiles.create({
              profileType: 'PacProfile',
              name: '',
              pacScript: script
            });
          })();
        }
        break;
      case 'fixed_servers':
        props = ['proxyForHttp', 'proxyForHttps', 'proxyForFtp', 'fallbackProxy', 'singleProxy'];
        proxies = {};
        for (j = 0, len = props.length; j < len; j++) {
          prop = props[j];
          result = OmegaPac.Profiles.pacResult(details.value.rules[prop]);
          if (prop === 'singleProxy' && (details.value.rules[prop] != null)) {
            proxies['fallbackProxy'] = result;
          } else {
            proxies[prop] = result;
          }
        }
        bypassSet = {};
        bypassCount = 0;
        if (details.value.rules.bypassList) {
          ref = details.value.rules.bypassList;
          for (k = 0, len1 = ref.length; k < len1; k++) {
            pattern = ref[k];
            bypassSet[pattern] = true;
            bypassCount++;
          }
        }
        if (bypassSet['<local>']) {
          ref1 = OmegaPac.Conditions.localHosts;
          for (l = 0, len2 = ref1.length; l < len2; l++) {
            host = ref1[l];
            if (!bypassSet[host]) {
              continue;
            }
            delete bypassSet[host];
            bypassCount--;
          }
        }
        profile = null;
        OmegaPac.Profiles.each(options, (function(_this) {
          return function(key, p) {
            var condition, len3, len4, m, n, ref2, rules;
            if (p.profileType !== 'FixedProfile') {
              return;
            }
            if (p.bypassList.length !== bypassCount) {
              return;
            }
            ref2 = p.bypassList;
            for (m = 0, len3 = ref2.length; m < len3; m++) {
              condition = ref2[m];
              if (!bypassSet[condition.pattern]) {
                return;
              }
            }
            rules = _this._fixedProfileConfig(p).rules;
            if (rules['singleProxy']) {
              rules['fallbackProxy'] = rules['singleProxy'];
              delete rules['singleProxy'];
            }
            if (rules == null) {
              return;
            }
            for (n = 0, len4 = props.length; n < len4; n++) {
              prop = props[n];
              if (rules[prop] || proxies[prop]) {
                if (OmegaPac.Profiles.pacResult(rules[prop]) !== proxies[prop]) {
                  return;
                }
              }
            }
            return profile = p;
          };
        })(this));
        if (profile) {
          return profile;
        } else {
          profile = OmegaPac.Profiles.create({
            profileType: 'FixedProfile',
            name: ''
          });
          for (m = 0, len3 = props.length; m < len3; m++) {
            prop = props[m];
            if (details.value.rules[prop]) {
              if (prop === 'singleProxy') {
                profile['fallbackProxy'] = details.value.rules[prop];
              } else {
                profile[prop] = details.value.rules[prop];
              }
            }
          }
          profile.bypassList = (function() {
            var results;
            results = [];
            for (pattern in bypassSet) {
              if (!hasProp.call(bypassSet, pattern)) continue;
              results.push({
                conditionType: 'BypassCondition',
                pattern: pattern
              });
            }
            return results;
          })();
          return profile;
        }
    }
  };

  return SettingsProxyImpl;

})(ProxyImpl);

module.exports = SettingsProxyImpl;


},{"../chrome_api":9,"./proxy_impl":18,"omega-target":8}],23:[function(require,module,exports){
var ChromeStorage, OmegaTarget, Promise, chromeApiPromisify,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
  hasProp = {}.hasOwnProperty;

chromeApiPromisify = require('./chrome_api').chromeApiPromisify;

OmegaTarget = require('omega-target');

Promise = OmegaTarget.Promise;

ChromeStorage = (function(superClass) {
  extend(ChromeStorage, superClass);

  ChromeStorage.parseStorageErrors = function(err) {
    var sustainedPerMinute;
    if (err != null ? err.message : void 0) {
      sustainedPerMinute = 'MAX_SUSTAINED_WRITE_OPERATIONS_PER_MINUTE';
      if (err.message.indexOf('QUOTA_BYTES_PER_ITEM') >= 0) {
        err = new OmegaTarget.Storage.QuotaExceededError();
        err.perItem = true;
      } else if (err.message.indexOf('QUOTA_BYTES') >= 0) {
        err = new OmegaTarget.Storage.QuotaExceededError();
      } else if (err.message.indexOf('MAX_ITEMS') >= 0) {
        err = new OmegaTarget.Storage.QuotaExceededError();
        err.maxItems = true;
      } else if (err.message.indexOf('MAX_WRITE_OPERATIONS_') >= 0) {
        err = new OmegaTarget.Storage.RateLimitExceededError();
        if (err.message.indexOf('MAX_WRITE_OPERATIONS_PER_HOUR') >= 0) {
          err.perHour = true;
        } else if (err.message.indexOf('MAX_WRITE_OPERATIONS_PER_MINUTE') >= 0) {
          err.perMinute = true;
        }
      } else if (err.message.indexOf(sustainedPerMinute) >= 0) {
        err = new OmegaTarget.Storage.RateLimitExceededError();
        err.perMinute = true;
        err.sustained = 10;
      } else if (err.message.indexOf('is not available') >= 0) {
        err = new OmegaTarget.Storage.StorageUnavailableError();
      } else if (err.message.indexOf('Please set webextensions.storage.sync.enabled to true') >= 0) {
        err = new OmegaTarget.Storage.StorageUnavailableError();
      }
    }
    return Promise.reject(err);
  };

  function ChromeStorage(areaName1) {
    var ref;
    this.areaName = areaName1;
    if (typeof browser !== "undefined" && browser !== null ? (ref = browser.storage) != null ? ref[this.areaName] : void 0 : void 0) {
      this.storage = browser.storage[this.areaName];
    } else {
      this.storage = {
        get: chromeApiPromisify(chrome.storage[this.areaName], 'get'),
        set: chromeApiPromisify(chrome.storage[this.areaName], 'set'),
        remove: chromeApiPromisify(chrome.storage[this.areaName], 'remove'),
        clear: chromeApiPromisify(chrome.storage[this.areaName], 'clear')
      };
    }
  }

  ChromeStorage.prototype.get = function(keys) {
    if (keys == null) {
      keys = null;
    }
    return Promise.resolve(this.storage.get(keys))["catch"](ChromeStorage.parseStorageErrors);
  };

  ChromeStorage.prototype.set = function(items) {
    if (Object.keys(items).length === 0) {
      return Promise.resolve({});
    }
    return Promise.resolve(this.storage.set(items))["catch"](ChromeStorage.parseStorageErrors);
  };

  ChromeStorage.prototype.remove = function(keys) {
    if (keys == null) {
      return Promise.resolve(this.storage.clear());
    }
    if (Array.isArray(keys) && keys.length === 0) {
      return Promise.resolve({});
    }
    return Promise.resolve(this.storage.remove(keys))["catch"](ChromeStorage.parseStorageErrors);
  };

  ChromeStorage.prototype.watch = function(keys, callback) {
    var area, base, i, id, key, keyMap, len, name, watcher;
    if ((base = ChromeStorage.watchers)[name = this.areaName] == null) {
      base[name] = {};
    }
    area = ChromeStorage.watchers[this.areaName];
    watcher = {
      keys: keys,
      callback: callback
    };
    id = Date.now().toString();
    while (area[id]) {
      id = Date.now().toString();
    }
    if (Array.isArray(keys)) {
      keyMap = {};
      for (i = 0, len = keys.length; i < len; i++) {
        key = keys[i];
        keyMap[key] = true;
      }
      keys = keyMap;
    }
    area[id] = {
      keys: keys,
      callback: callback
    };
    if (!ChromeStorage.onChangedListenerInstalled) {
      chrome.storage.onChanged.addListener(ChromeStorage.onChangedListener);
      ChromeStorage.onChangedListenerInstalled = true;
    }
    return function() {
      return delete area[id];
    };
  };

  ChromeStorage.onChangedListener = function(changes, areaName) {
    var _, change, key, map, match, ref, results, watcher;
    map = null;
    ref = ChromeStorage.watchers[areaName];
    results = [];
    for (_ in ref) {
      watcher = ref[_];
      match = watcher.keys === null;
      if (!match) {
        for (key in changes) {
          if (!hasProp.call(changes, key)) continue;
          if (watcher.keys[key]) {
            match = true;
            break;
          }
        }
      }
      if (match) {
        if (map == null) {
          map = {};
          for (key in changes) {
            if (!hasProp.call(changes, key)) continue;
            change = changes[key];
            map[key] = change.newValue;
          }
        }
        results.push(watcher.callback(map));
      } else {
        results.push(void 0);
      }
    }
    return results;
  };

  ChromeStorage.onChangedListenerInstalled = false;

  ChromeStorage.watchers = {};

  return ChromeStorage;

})(OmegaTarget.Storage);

module.exports = ChromeStorage;


},{"./chrome_api":9,"omega-target":8}],24:[function(require,module,exports){
var ChromePort, OmegaPac, OmegaTarget, Promise, SwitchySharp;

OmegaTarget = require('omega-target');

OmegaPac = OmegaTarget.OmegaPac;

Promise = OmegaTarget.Promise;

ChromePort = require('./chrome_port');

module.exports = SwitchySharp = (function() {
  function SwitchySharp() {}

  SwitchySharp.extId = 'dpplabbmogkhghncfbfdeeokoefdjegm';

  SwitchySharp.prototype.port = null;

  SwitchySharp.prototype.monitor = function(action) {
    if (location.href.substr(0, 4) === 'moz-') {
      return;
    }
    if ((typeof port === "undefined" || port === null) && (this._monitorTimerId == null)) {
      this._monitorTimerId = setInterval(this._connect.bind(this), 5000);
      if (action !== 'reconnect') {
        return this._connect();
      }
    }
  };

  SwitchySharp.prototype.getOptions = function() {
    if (!this._getOptions) {
      this._getOptions = new Promise((function(_this) {
        return function(resolve) {
          _this._getOptionsResolver = resolve;
          return _this.monitor();
        };
      })(this));
    }
    return this._getOptions;
  };

  SwitchySharp.prototype._getOptions = null;

  SwitchySharp.prototype._getOptionsResolver = null;

  SwitchySharp.prototype._monitorTimerId = null;

  SwitchySharp.prototype._onMessage = function(msg) {
    if (this._monitorTimerId) {
      clearInterval(this._monitorTimerId);
      this._monitorTimerId = null;
    }
    switch (msg != null ? msg.action : void 0) {
      case 'state':
        OmegaTarget.Log.log(msg);
        if (this._getOptionsResolver) {
          return this.port.postMessage({
            action: 'getOptions'
          });
        }
        break;
      case 'options':
        if (typeof this._getOptionsResolver === "function") {
          this._getOptionsResolver(msg.options);
        }
        return this._getOptionsResolver = null;
    }
  };

  SwitchySharp.prototype._onDisconnect = function(msg) {
    this.port = null;
    this._getOptions = null;
    this._getOptionsResolver = null;
    return this.monitor('reconnect');
  };

  SwitchySharp.prototype._connect = function() {
    var _, ref;
    if (!this.port) {
      this.port = new ChromePort(chrome.runtime.connect(SwitchySharp.extId));
      this.port.onDisconnect.addListener(this._onDisconnect.bind(this));
      if ((ref = this.port) != null) {
        ref.onMessage.addListener(this._onMessage.bind(this));
      }
    }
    try {
      this.port.postMessage({
        action: 'disable'
      });
    } catch (error) {
      _ = error;
      this.port = null;
    }
    return this.port != null;
  };

  return SwitchySharp;

})();


},{"./chrome_port":10,"omega-target":8}],25:[function(require,module,exports){
var ChromeSyncStorage, OmegaTarget, Promise, _processPush, getAll, getGist, getLastCommit, gistHost, gistId, gistToken, isPulling, isPushing, mainLetters, onChangedListenerInstalled, optionFilename, optionsSync, processCheckCommit, processPull, processPush, state, updateGist,
  hasProp = {}.hasOwnProperty,
  extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; };

OmegaTarget = require('omega-target');

Promise = OmegaTarget.Promise;

onChangedListenerInstalled = false;

isPulling = false;

isPushing = false;

state = null;

optionsSync = null;

mainLetters = ['Z', 'e', 'r', 'o', 'O', 'm', 'e', 'g', 'a'];

optionFilename = mainLetters.concat(['.json']).join('');

gistId = '';

gistToken = '';

gistHost = 'https://api.github.com';

processCheckCommit = function() {
  return getLastCommit(gistId).then(function(remoteCommit) {
    return state.set({
      'lastGistSync': Date.now()
    }).then(function() {
      return state.get({
        'lastGistCommit': '-2'
      }).then(function(arg) {
        var lastGistCommit;
        lastGistCommit = arg.lastGistCommit;
        return lastGistCommit !== remoteCommit;
      });
    });
  })["catch"](function() {
    return true;
  });
};

processPull = function(syncStore) {
  return new Promise(function(resolve, reject) {
    return getGist(gistId).then(function(gist) {
      var changes;
      if (isPushing) {
        return resolve({
          changes: {}
        });
      } else {
        changes = {};
        return getAll(syncStore).then(function(data) {
          var e, key, options, optionsStr, ref, ref1, target, val;
          try {
            optionsStr = (ref = gist.files[optionFilename]) != null ? ref.content : void 0;
            options = JSON.parse(optionsStr);
            for (key in data) {
              if (!hasProp.call(data, key)) continue;
              val = data[key];
              changes[key] = {
                oldValue: val
              };
            }
            for (key in options) {
              if (!hasProp.call(options, key)) continue;
              val = options[key];
              target = changes[key];
              if (!target) {
                changes[key] = {};
                target = changes[key];
              }
              target.newValue = val;
            }
            for (key in changes) {
              if (!hasProp.call(changes, key)) continue;
              val = changes[key];
              if (JSON.stringify(val.oldValue) === JSON.stringify(val.newValue)) {
                delete changes[key];
              }
            }
          } catch (error) {
            e = error;
            changes = {};
          }
          if (state != null) {
            state.set({
              'lastGistCommit': (ref1 = gist.history[0]) != null ? ref1.version : void 0,
              'lastGistState': 'success',
              'lastGistSync': Date.now()
            });
          }
          return resolve({
            changes: changes,
            remoteOptions: options
          });
        });
      }
    })["catch"](function(e) {
      if (state != null) {
        state.set({
          'lastGistSync': Date.now(),
          'lastGistState': 'fail: ' + e
        });
      }
      return resolve({
        changes: {}
      });
    });
  });
};

getAll = function(syncStore) {
  return idbKeyval.entries(syncStore).then(function(entries) {
    var data;
    data = {};
    entries.forEach(function(entry) {
      return data[entry[0]] = entry[1];
    });
    return data;
  });
};

_processPush = function() {
  var syncStore;
  if (processPush.sequence.length > 0) {
    syncStore = processPush.sequence[processPush.sequence.length - 1];
    processPush.sequence.length = 0;
    return getAll(syncStore).then(function(data) {
      return updateGist(gistId, data);
    }).then(function() {
      return _processPush();
    });
  } else {
    return isPushing = false;
  }
};

processPush = function(syncStore) {
  processPush.sequence.push(syncStore);
  if (isPushing) {
    return;
  }
  isPushing = true;
  return setTimeout(_processPush, 600);
};

processPush.sequence = [];

getLastCommit = function(gistId) {
  return fetch(gistHost + '/gists/' + gistId + '/commits?per_page=1', {
    headers: {
      "Accept": "application/vnd.github+json",
      "Authorization": "Bearer " + gistToken,
      "X-GitHub-Api-Version": "2022-11-28"
    }
  }).then(function(res) {
    return res.json();
  }).then(function(data) {
    var ref;
    if (data.message) {
      throw data.message;
    }
    return (ref = data[0]) != null ? ref.version : void 0;
  });
};

getGist = function(gistId) {
  return fetch(gistHost + '/gists/' + gistId, {
    headers: {
      "Accept": "application/vnd.github+json",
      "Authorization": "Bearer " + gistToken,
      "X-GitHub-Api-Version": "2022-11-28"
    }
  }).then(function(res) {
    return res.json();
  }).then(function(data) {
    if (data.message) {
      throw data.message;
    }
    return data;
  });
};

updateGist = function(gistId, options) {
  var postBody;
  postBody = {
    description: mainLetters.concat([' Sync']).join(''),
    files: {}
  };
  postBody.files[optionFilename] = {
    content: JSON.stringify(options, null, 4)
  };
  return fetch(gistHost + '/gists/' + gistId, {
    headers: {
      "Accept": "application/vnd.github+json",
      "Authorization": "Bearer " + gistToken,
      "X-GitHub-Api-Version": "2022-11-28"
    },
    "method": "PATCH",
    body: JSON.stringify(postBody)
  }).then(function(res) {
    return res.json();
  }).then(function(data) {
    var lastGistCommit, ref;
    if (data.status === "404") {
      throw new Error("The token with Gist permission is required.");
    }
    if (data.message) {
      throw data.message;
    }
    lastGistCommit = (ref = data.history[0]) != null ? ref.version : void 0;
    if (state != null) {
      state.set({
        'lastGistCommit': lastGistCommit,
        'lastGistState': 'success',
        'lastGistSync': Date.now()
      }).then(function() {
        return optionsSync != null ? optionsSync.updateBuiltInSyncConfigIf({
          lastGistCommit: lastGistCommit
        }) : void 0;
      });
    }
    return data;
  })["catch"](function(e) {
    if (state != null) {
      state.set({
        'lastGistState': 'fail: ' + e,
        'lastGistSync': Date.now()
      });
    }
    return console.error('update gist fail::', e);
  });
};

ChromeSyncStorage = (function(superClass) {
  extend(ChromeSyncStorage, superClass);

  ChromeSyncStorage.parseStorageErrors = function(err) {
    return Promise.reject(err);
  };

  function ChromeSyncStorage(areaName1, _state) {
    var _remove, clear, get, remove, set, syncStore;
    this.areaName = areaName1;
    state = _state;
    syncStore = idbKeyval.createStore('sync-store', 'sync');
    this.syncStore = syncStore;
    get = function(key) {
      return new Promise(function(resolve, reject) {
        return getAll(syncStore).then(function(data) {
          var result;
          result = {};
          if (Array.isArray(key)) {
            key.forEach(_key(function() {
              return result[_key] = data[_key];
            }));
          } else if (key === null) {
            result = data;
          } else {
            result[key] = data[key];
          }
          return resolve(result);
        });
      });
    };
    set = function(record) {
      return new Promise(function(resolve, reject) {
        var e, entries, key, value;
        try {
          if (!record || typeof record !== 'object' || Array.isArray(record)) {
            throw new SyntaxError('Only Object with key value pairs are acceptable');
          }
          entries = [];
          for (key in record) {
            if (!hasProp.call(record, key)) continue;
            value = record[key];
            entries.push([key, value]);
          }
          return idbKeyval.setMany(entries, syncStore).then(function() {
            processPush(syncStore);
            return resolve(record);
          });
        } catch (error) {
          e = error;
          return reject(e);
        }
      });
    };
    _remove = function(key) {
      if (Array.isArray(key)) {
        return Promise.resolve(idbKeyval.delMany(key, syncStore));
      } else {
        return Promise.resolve(idbKeyval.del(key, syncStore));
      }
    };
    remove = function(key) {
      return Promise.resolve(_remove(key).then(function() {
        processPush(syncStore);
      }));
    };
    clear = function() {
      return Promise.resolve(idbKeyval.clear(syncStore).then(function() {
        processPush(syncStore);
      }));
    };
    this.storage = {
      get: get,
      set: set,
      remove: remove,
      clear: clear
    };
  }

  ChromeSyncStorage.prototype.get = function(keys) {
    if (keys == null) {
      keys = null;
    }
    return Promise.resolve(this.storage.get(keys))["catch"](ChromeSyncStorage.parseStorageErrors);
  };

  ChromeSyncStorage.prototype.set = function(items) {
    if (Object.keys(items).length === 0) {
      return Promise.resolve({});
    }
    return Promise.resolve(this.storage.set(items))["catch"](ChromeSyncStorage.parseStorageErrors);
  };

  ChromeSyncStorage.prototype.remove = function(keys) {
    if (keys == null) {
      return Promise.resolve(this.storage.clear());
    }
    if (Array.isArray(keys) && keys.length === 0) {
      return Promise.resolve({});
    }
    return Promise.resolve(this.storage.remove(keys))["catch"](ChromeSyncStorage.parseStorageErrors);
  };

  ChromeSyncStorage.prototype.destroy = function() {
    return idbKeyval.clear(this.syncStore);
  };

  ChromeSyncStorage.prototype.flush = function(arg) {
    var data, entries, key, result, value;
    data = arg.data;
    entries = [];
    result = null;
    if (data && data.schemaVersion) {
      for (key in data) {
        if (!hasProp.call(data, key)) continue;
        value = data[key];
        entries.push([key, value]);
      }
      result = idbKeyval.clear(this.syncStore).then((function(_this) {
        return function() {
          return idbKeyval.setMany(entries, _this.syncStore);
        };
      })(this));
    }
    return Promise.resolve(result);
  };

  ChromeSyncStorage.prototype.init = function(args) {
    optionsSync = args.optionsSync;
    state = args.state;
    gistId = args.gistId || '';
    if (gistId.indexOf('/') >= 0) {
      gistId = gistId.replace(/\/+$/, '');
      gistId = gistId.split('/');
      gistId = gistId[gistId.length - 1];
    }
    gistToken = args.gistToken;
    return new Promise(function(resolve, reject) {
      return getLastCommit(gistId).then(function(lastGistCommit) {
        if (args.withRemoteData) {
          return getGist(gistId).then(function(gist) {
            var e, options, optionsStr;
            try {
              optionsStr = gist.files[optionFilename].content;
              options = JSON.parse(optionsStr);
              return resolve({
                options: options,
                lastGistCommit: lastGistCommit
              });
            } catch (error) {
              e = error;
              return resolve({});
            }
          });
        } else {
          return resolve({});
        }
      })["catch"](function(e) {
        return reject(e);
      });
    });
  };

  ChromeSyncStorage.prototype.checkChange = function(opts) {
    if (opts == null) {
      opts = {};
    }
    isPulling = true;
    return processCheckCommit().then((function(_this) {
      return function(isChanged) {
        if (isChanged || opts.force) {
          return processPull(_this.syncStore).then(function(arg) {
            var changes, remoteOptions;
            changes = arg.changes, remoteOptions = arg.remoteOptions;
            return _this.flush({
              data: remoteOptions
            }).then(function() {
              isPulling = false;
              return ChromeSyncStorage.onChangedListener(changes, _this.areaName, opts);
            });
          });
        } else {
          console.log('no changed');
          return isPulling = false;
        }
      };
    })(this));
  };

  ChromeSyncStorage.prototype.watch = function(keys, callback) {
    var area, base, enableSync, i, id, key, keyMap, len, name, watcher;
    chrome.alarms.create('omega.syncCheck', {
      periodInMinutes: 5
    });
    if ((base = ChromeSyncStorage.watchers)[name = this.areaName] == null) {
      base[name] = {};
    }
    area = ChromeSyncStorage.watchers[this.areaName];
    watcher = {
      keys: keys,
      callback: callback
    };
    enableSync = true;
    id = Date.now().toString();
    while (area[id]) {
      id = Date.now().toString();
    }
    if (Array.isArray(keys)) {
      keyMap = {};
      for (i = 0, len = keys.length; i < len; i++) {
        key = keys[i];
        keyMap[key] = true;
      }
      keys = keyMap;
    }
    area[id] = {
      keys: keys,
      callback: callback
    };
    if (!onChangedListenerInstalled) {
      this.checkChange();
      chrome.alarms.onAlarm.addListener((function(_this) {
        return function(alarm) {
          if (!enableSync) {
            return;
          }
          if (isPulling) {
            return;
          }
          switch (alarm.name) {
            case 'omega.syncCheck':
              return _this.checkChange();
          }
        };
      })(this));
      onChangedListenerInstalled = true;
    }
    return function() {
      enableSync = false;
      return delete area[id];
    };
  };

  ChromeSyncStorage.onChangedListener = function(changes, areaName, opts) {
    var _, change, key, map, match, ref, results, watcher;
    if (opts == null) {
      opts = {};
    }
    map = null;
    ref = ChromeSyncStorage.watchers[areaName];
    results = [];
    for (_ in ref) {
      watcher = ref[_];
      match = watcher.keys === null;
      if (!match) {
        for (key in changes) {
          if (!hasProp.call(changes, key)) continue;
          if (watcher.keys[key]) {
            match = true;
            break;
          }
        }
      }
      if (match) {
        if (map == null) {
          map = {};
          for (key in changes) {
            if (!hasProp.call(changes, key)) continue;
            change = changes[key];
            map[key] = change.newValue;
          }
        }
        results.push(watcher.callback(map, opts));
      } else {
        results.push(void 0);
      }
    }
    return results;
  };

  ChromeSyncStorage.watchers = {};

  return ChromeSyncStorage;

})(OmegaTarget.Storage);

module.exports = ChromeSyncStorage;


},{"omega-target":8}],26:[function(require,module,exports){
var ChromeTabs,
  hasProp = {}.hasOwnProperty;

ChromeTabs = (function() {
  ChromeTabs.prototype._defaultAction = null;

  ChromeTabs.prototype._badgeTab = null;

  function ChromeTabs(actionForUrl) {
    this.actionForUrl = actionForUrl;
    this._dirtyTabs = {};
    return;
  }

  ChromeTabs.prototype.ignoreError = function() {
    chrome.runtime.lastError;
  };

  ChromeTabs.prototype.watch = function() {
    chrome.tabs.onUpdated.addListener(this.onUpdated.bind(this));
    return chrome.tabs.onActivated.addListener((function(_this) {
      return function(info) {
        return chrome.tabs.get(info.tabId, function(tab) {
          if (chrome.runtime.lastError) {
            return;
          }
          if (_this._dirtyTabs.hasOwnProperty(info.tabId)) {
            return _this.onUpdated(tab.id, {}, tab);
          }
        });
      };
    })(this));
  };

  ChromeTabs.prototype.resetAll = function(action) {
    var title;
    this._defaultAction = action;
    chrome.tabs.query({}, (function(_this) {
      return function(tabs) {
        _this._dirtyTabs = {};
        return tabs.forEach(function(tab) {
          _this._dirtyTabs[tab.id] = tab.id;
          if (tab.active) {
            return _this.onUpdated(tab.id, {}, tab);
          }
        });
      };
    })(this));
    title = this._canSetPopup() ? action.title : action.shortTitle;
    chrome.action.setTitle({
      title: title
    });
    return this.setIcon(action.icon);
  };

  ChromeTabs.prototype.onUpdated = function(tabId, changeInfo, tab) {
    if (this._dirtyTabs.hasOwnProperty(tab.id)) {
      delete this._dirtyTabs[tab.id];
    } else if ((changeInfo.url == null) && changeInfo.status === "complete") {
      return;
    }
    return this.processTab(tab, changeInfo);
  };

  ChromeTabs.prototype.processTab = function(tab, changeInfo) {
    var base, id, ref;
    if (this._badgeTab) {
      ref = this._badgeTab;
      for (id in ref) {
        if (!hasProp.call(ref, id)) continue;
        try {
          if (typeof (base = chrome.action).setBadgeText === "function") {
            base.setBadgeText({
              text: '',
              tabId: id
            });
          }
        } catch (error) {}
        this._badgeTab = null;
      }
    }
    if ((tab.url == null) || tab.url.indexOf("chrome") === 0) {
      if (this._defaultAction) {
        chrome.action.setTitle({
          title: this._defaultAction.title,
          tabId: tab.id
        });
        this.clearIcon(tab.id);
      }
      return;
    }
    return this.actionForUrl(tab.url).then((function(_this) {
      return function(action) {
        var base1, base2, title;
        if (!action) {
          _this.clearIcon(tab.id);
          if (typeof (base1 = chrome.action).setBadgeText === "function") {
            base1.setBadgeText({
              text: '',
              tabId: tab.id
            });
          }
          return;
        }
        _this.setIcon(action.icon, tab.id);
        title = _this._canSetPopup() ? action.title : action.shortTitle;
        if (action.badgeText) {
          if (typeof (base2 = chrome.action).setBadgeText === "function") {
            base2.setBadgeText({
              text: action.badgeText,
              tabId: tab.id
            });
          }
        }
        return chrome.action.setTitle({
          title: title,
          tabId: tab.id
        });
      };
    })(this))["catch"](function(e) {
      return console.log('error:', e);
    });
  };

  ChromeTabs.prototype.setTabBadge = function(tab, badge) {
    var base, base1;
    if (this._badgeTab == null) {
      this._badgeTab = {};
    }
    this._badgeTab[tab.id] = true;
    if (typeof (base = chrome.action).setBadgeText === "function") {
      base.setBadgeText({
        text: badge.text,
        tabId: tab.id
      });
    }
    return typeof (base1 = chrome.action).setBadgeBackgroundColor === "function" ? base1.setBadgeBackgroundColor({
      color: badge.color,
      tabId: tab.id
    }) : void 0;
  };

  ChromeTabs.prototype.setIcon = function(icon, tabId) {
    var params;
    if (icon == null) {
      return;
    }
    params = {
      imageData: icon
    };
    if (tabId != null) {
      params.tabId = tabId;
    }
    return this._chromeSetIcon(params);
  };

  ChromeTabs.prototype._canSetPopup = function() {
    return chrome.action.setPopup != null;
  };

  ChromeTabs.prototype._chromeSetIcon = function(params) {
    var _, base, base1;
    try {
      return typeof (base = chrome.action).setIcon === "function" ? base.setIcon(params, this.ignoreError) : void 0;
    } catch (error) {
      _ = error;
      params.imageData = {
        19: params.imageData[19],
        38: params.imageData[38]
      };
      return typeof (base1 = chrome.action).setIcon === "function" ? base1.setIcon(params, this.ignoreError) : void 0;
    }
  };

  ChromeTabs.prototype.clearIcon = function(tabId) {
    var ref;
    if (((ref = this._defaultAction) != null ? ref.icon : void 0) == null) {
      return;
    }
    return this._chromeSetIcon({
      imageData: this._defaultAction.icon,
      tabId: tabId
    }, this.ignoreError);
  };

  return ChromeTabs;

})();

module.exports = ChromeTabs;


},{}],27:[function(require,module,exports){
var Heap, MAXREQUESTCACHE, Url, WebRequestMonitor,
  hasProp = {}.hasOwnProperty;

Heap = require('heap');

Url = require('url');

MAXREQUESTCACHE = 1000;

module.exports = WebRequestMonitor = (function() {
  function WebRequestMonitor(getSummaryId) {
    this.getSummaryId = getSummaryId;
    this._requests = {};
    this._recentRequests = new Heap(function(a, b) {
      return a._startTime - b._startTime;
    });
    this._callbacks = [];
    this._tabCallbacks = [];
    this.tabInfo = {};
  }

  WebRequestMonitor.prototype._callbacks = null;

  WebRequestMonitor.prototype.watching = false;

  WebRequestMonitor.prototype.timer = null;

  WebRequestMonitor.prototype.watch = function(callback) {
    var extraInfoSpec;
    this._callbacks.push(callback);
    if (this.watching) {
      return;
    }
    if (!chrome.webRequest) {
      console.log('Request monitor disabled! No webRequest permission.');
      return;
    }
    chrome.webRequest.onBeforeRequest.addListener(this._requestStart.bind(this), {
      urls: ['<all_urls>']
    });
    chrome.webRequest.onHeadersReceived.addListener(this._requestHeadersReceived.bind(this), {
      urls: ['<all_urls>']
    });
    chrome.webRequest.onBeforeRedirect.addListener(this._requestRedirected.bind(this), {
      urls: ['<all_urls>']
    });
    extraInfoSpec = ["responseHeaders"];
    if (!globalThis.localStorage) {
      extraInfoSpec.push('extraHeaders');
    }
    chrome.webRequest.onCompleted.addListener(this._requestDone.bind(this), {
      urls: ['<all_urls>']
    }, extraInfoSpec);
    chrome.webRequest.onErrorOccurred.addListener(this._requestError.bind(this), {
      urls: ['<all_urls>']
    });
    return this.watching = true;
  };

  WebRequestMonitor.prototype._requests = null;

  WebRequestMonitor.prototype._recentRequests = null;

  WebRequestMonitor.prototype._requestStart = function(req) {
    var callback, i, len, ref, results;
    if (req.tabId < 0) {
      return;
    }
    req._startTime = Date.now();
    this._requests[req.requestId] = req;
    this._recentRequests.push(req);
    if (this.timer == null) {
      this.timer = setInterval(this._tick.bind(this), 1000);
    }
    ref = this._callbacks;
    results = [];
    for (i = 0, len = ref.length; i < len; i++) {
      callback = ref[i];
      results.push(callback('start', req));
    }
    return results;
  };

  WebRequestMonitor.prototype._tick = function() {
    var callback, i, len, now, ref, req, reqInfo, results;
    now = Date.now();
    results = [];
    while ((req = this._recentRequests.peek())) {
      reqInfo = this._requests[req.requestId];
      if (reqInfo && !reqInfo.noTimeout) {
        if (now - req._startTime < 5000) {
          break;
        } else {
          reqInfo.timeoutCalled = true;
          ref = this._callbacks;
          for (i = 0, len = ref.length; i < len; i++) {
            callback = ref[i];
            callback('timeout', reqInfo);
          }
        }
      }
      results.push(this._recentRequests.pop());
    }
    return results;
  };

  WebRequestMonitor.prototype._requestHeadersReceived = function(req) {
    var callback, i, len, ref, reqInfo, results;
    reqInfo = this._requests[req.requestId];
    if (!reqInfo) {
      return;
    }
    reqInfo.noTimeout = true;
    if (reqInfo.timeoutCalled) {
      ref = this._callbacks;
      results = [];
      for (i = 0, len = ref.length; i < len; i++) {
        callback = ref[i];
        results.push(callback('ongoing', req));
      }
      return results;
    }
  };

  WebRequestMonitor.prototype._requestRedirected = function(req) {
    var url;
    url = req.redirectUrl;
    if (!url) {
      return;
    }
    if (url.indexOf('data:') === 0 || url.indexOf('about:') === 0) {
      return this._requestDone(req);
    }
  };

  WebRequestMonitor.prototype._requestError = function(req) {
    var callback, i, j, len, len1, ref, ref1, reqInfo, results;
    reqInfo = this._requests[req.requestId];
    delete this._requests[req.requestId];
    if (req.tabId < 0) {
      return;
    }
    if (req.error === 'net::ERR_INCOMPLETE_CHUNKED_ENCODING') {
      return;
    }
    if (req.error.indexOf('BLOCKED') >= 0) {
      return;
    }
    if (req.error.indexOf('net::ERR_FILE_') === 0) {
      return;
    }
    if (req.error.indexOf('NS_ERROR_ABORT') === 0) {
      return;
    }
    if (req.url.indexOf('file:') === 0) {
      return;
    }
    if (req.url.indexOf('chrome') === 0) {
      return;
    }
    if (req.url.indexOf('about:') === 0) {
      return;
    }
    if (req.url.indexOf('moz-') === 0) {
      return;
    }
    if (req.url.indexOf('://127.0.0.1') > 0) {
      return;
    }
    if (!reqInfo) {
      return;
    }
    if (req.error === 'net::ERR_ABORTED') {
      if (reqInfo.timeoutCalled && !reqInfo.noTimeout) {
        ref = this._callbacks;
        for (i = 0, len = ref.length; i < len; i++) {
          callback = ref[i];
          callback('timeoutAbort', req);
        }
      }
      return;
    }
    ref1 = this._callbacks;
    results = [];
    for (j = 0, len1 = ref1.length; j < len1; j++) {
      callback = ref1[j];
      results.push(callback('error', req));
    }
    return results;
  };

  WebRequestMonitor.prototype._requestDone = function(req) {
    var callback, i, len, ref;
    ref = this._callbacks;
    for (i = 0, len = ref.length; i < len; i++) {
      callback = ref[i];
      callback('done', req);
    }
    return delete this._requests[req.requestId];
  };

  WebRequestMonitor.prototype.eventCategory = {
    start: 'ongoing',
    ongoing: 'ongoing',
    timeout: 'error',
    error: 'error',
    timeoutAbort: 'error',
    done: 'done'
  };

  WebRequestMonitor.prototype.tabsWatching = false;

  WebRequestMonitor.prototype._tabCallbacks = null;

  WebRequestMonitor.prototype.watchTabs = function(callback) {
    var ref;
    this._tabCallbacks.push(callback);
    if (this.tabsWatching) {
      return;
    }
    this.watch(this.setTabRequestInfo.bind(this));
    this.tabsWatching = true;
    chrome.tabs.onCreated.addListener((function(_this) {
      return function(tab) {
        if (!tab.id) {
          return;
        }
        return _this.tabInfo[tab.id] = _this._newTabInfo();
      };
    })(this));
    chrome.tabs.onRemoved.addListener((function(_this) {
      return function(tabId) {
        return delete _this.tabInfo[tabId];
      };
    })(this));
    if ((ref = chrome.tabs.onReplaced) != null) {
      ref.addListener((function(_this) {
        return function(added, removed) {
          var base;
          if ((base = _this.tabInfo)[added] == null) {
            base[added] = _this._newTabInfo();
          }
          return delete _this.tabInfo[removed];
        };
      })(this));
    }
    chrome.tabs.onUpdated.addListener((function(_this) {
      return function(tabId, changeInfo, tab) {
        var base, i, info, len, name, ref1, results;
        info = (base = _this.tabInfo)[name = tab.id] != null ? base[name] : base[name] = _this._newTabInfo();
        if (!info) {
          return;
        }
        ref1 = _this._tabCallbacks;
        results = [];
        for (i = 0, len = ref1.length; i < len; i++) {
          callback = ref1[i];
          results.push(callback(tab.id, info, null, 'updated'));
        }
        return results;
      };
    })(this));
    return chrome.tabs.query({}, (function(_this) {
      return function(tabs) {
        var base, i, len, name, results, tab;
        results = [];
        for (i = 0, len = tabs.length; i < len; i++) {
          tab = tabs[i];
          results.push((base = _this.tabInfo)[name = tab.id] != null ? base[name] : base[name] = _this._newTabInfo());
        }
        return results;
      };
    })(this));
  };

  WebRequestMonitor.prototype._newTabInfo = function() {
    return {
      requests: {},
      requestCount: 0,
      requestStatus: {},
      ongoingCount: 0,
      errorCount: 0,
      doneCount: 0,
      summary: {}
    };
  };

  WebRequestMonitor.prototype.setTabRequestInfo = function(status, req) {
    var callback, hostname, i, id, info, key, len, nowTimeStamp, oldStatus, ref, ref1, reqInfo, results, statusInfo, statusObj, summaryItem, value;
    info = this.tabInfo[req.tabId];
    if (info) {
      if (status === 'start' && req.type === 'main_frame') {
        if (req.url.indexOf('chrome://errorpage/') !== 0) {
          ref = this._newTabInfo();
          for (key in ref) {
            if (!hasProp.call(ref, key)) continue;
            value = ref[key];
            info[key] = value;
          }
        }
      }
      if (info.requestCount > MAXREQUESTCACHE) {
        nowTimeStamp = Date.now();
        Object.keys(info.requests).forEach(function(requestId) {
          var _request, duration;
          if (requestId === req.requestId) {
            return;
          }
          if (info.requestStatus[requestId] === 'done') {
            delete info.requests[requestId];
            delete info.requestStatus[requestId];
          }
          _request = info.requests[requestId];
          if (_request != null ? _request.timeStamp : void 0) {
            duration = nowTimeStamp - _request.timeStamp;
            if (duration > 10 * 60 * 1000) {
              delete info.requests[requestId];
              return delete info.requestStatus[requestId];
            }
          }
        });
        info.requestCount = Object.keys(info.requests).length;
        if (info.requestCount > MAXREQUESTCACHE) {
          this.tabInfo[tab.id] = this._newTabInfo();
          return;
        }
      }
      reqInfo = info.requests[req.requestId] || {};
      statusObj = {};
      statusObj[status] = req.timeStamp || Date.now();
      statusInfo = Object.assign({}, reqInfo.statusInfo, statusObj);
      info.requests[req.requestId] = Object.assign({}, info.requests[req.requestId], req, {
        statusInfo: statusInfo
      });
      if ((oldStatus = info.requestStatus[req.requestId])) {
        info[this.eventCategory[oldStatus] + 'Count']--;
      } else {
        if (status === 'timeoutAbort') {
          return;
        }
        info.requestCount++;
      }
      info.requestStatus[req.requestId] = status;
      info[this.eventCategory[status] + 'Count']++;
      id = typeof this.getSummaryId === "function" ? this.getSummaryId(req) : void 0;
      if (id != null) {
        if (this.eventCategory[status] === 'error') {
          if (this.eventCategory[oldStatus] !== 'error') {
            summaryItem = info.summary[id];
            if (summaryItem == null) {
              hostname = Url.parse(req.url).hostname;
              summaryItem = info.summary[id] = {
                baseDomain: OmegaPac.wildcardForDomain(hostname),
                errorCount: 0
              };
            }
            summaryItem.errorCount++;
          }
        } else if (this.eventCategory[oldStatus] === 'error') {
          summaryItem = info.summary[id];
          if (summaryItem != null) {
            summaryItem.errorCount--;
          }
        }
      }
      ref1 = this._tabCallbacks;
      results = [];
      for (i = 0, len = ref1.length; i < len; i++) {
        callback = ref1[i];
        results.push(callback(req.tabId, info, req, status));
      }
      return results;
    }
  };

  return WebRequestMonitor;

})();


},{"heap":1,"url":7}],"OmegaTargetChromium":[function(require,module,exports){
module.exports = require('./src/module');


},{"./src/module":13}]},{},["OmegaTargetChromium"])("OmegaTargetChromium")
});
