"""
通用代理浏览器测试脚本 - 支持动态配置
演示如何通过程序传递代理配置，而不是硬编码在扩展中
"""

import time
from dataimpulse_browser import UniversalProxyBrowser


def main():
    print("🚀 通用代理浏览器测试 - 动态配置")
    print("=" * 50)
    
    # 从代理URL提取的凭据
    USERNAME = "0465846b31e1f583b17c__cr.us"
    PASSWORD = "16af34bf75f0573a"
    
    print(f"📋 配置信息:")
    print(f"   用户名: {USERNAME}")
    print(f"   密码: {PASSWORD}")
    print(f"   代理服务器: gw.dataimpulse.com")
    print(f"   端口: 19965, 19966")
    print()
    
    try:
        # 创建浏览器实例
        print("🔧 启动通用代理浏览器...")
        browser = UniversalProxyBrowser("universal_test")
        
        print("\n" + "="*50)
        print("步骤1: 动态添加代理配置")
        print("="*50)
        
        # 动态添加代理配置（不硬编码在扩展中）
        success1 = browser.add_proxy_config(
            config_id="dataimpulse_19965",
            host="gw.dataimpulse.com",
            port=19965,
            name="DataImpulse Proxy 19965"
        )
        
        success2 = browser.add_proxy_config(
            config_id="dataimpulse_19966", 
            host="gw.dataimpulse.com",
            port=19966,
            name="DataImpulse Proxy 19966"
        )
        
        if not (success1 and success2):
            print("❌ 代理配置添加失败")
            return
        
        print("✅ 代理配置添加成功")
        
        print("\n" + "="*50)
        print("步骤2: 测试IP和代理切换")
        print("="*50)
        
        # 测试初始IP
        print("\n🌐 测试初始IP:")
        initial_ip = browser.test_ip()
        
        # 切换到第一个代理
        print("\n⚡ 切换到代理 19965:")
        if browser.switch_to_proxy("dataimpulse_19965", USERNAME, PASSWORD):
            print("✅ 代理切换成功，等待生效...")
            time.sleep(3)
            proxy1_ip = browser.test_ip()
            
            if proxy1_ip != initial_ip:
                print(f"🎉 IP成功变更: {initial_ip} → {proxy1_ip}")
            else:
                print("⚠️ IP未变更，可能代理未生效")
        
        # 访问目标网站验证
        print("\n🌐 访问 https://api.ipify.org/ 验证:")
        try:
            browser.get('https://api.ipify.org/', timeout=10)
            print("✅ 成功访问 api.ipify.org")
        except Exception as e:
            print(f"❌ 访问失败: {e}")
        
        # 切换到第二个代理
        print("\n⚡ 切换到代理 19966:")
        if browser.switch_to_proxy("dataimpulse_19966", USERNAME, PASSWORD):
            print("✅ 代理切换成功，等待生效...")
            time.sleep(3)
            proxy2_ip = browser.test_ip()
            print(f"🔄 IP变化: {proxy1_ip if 'proxy1_ip' in locals() else initial_ip} → {proxy2_ip}")
        
        # 禁用代理
        print("\n🚫 禁用代理，恢复直连:")
        if browser.disable_proxy():
            print("✅ 代理禁用成功，等待恢复...")
            time.sleep(3)
            final_ip = browser.test_ip()
            print(f"🔄 IP恢复: {proxy2_ip if 'proxy2_ip' in locals() else 'Unknown'} → {final_ip}")
        
        # 总结
        print("\n" + "="*50)
        print("🎯 测试总结")
        print("="*50)
        print(f"初始IP: {initial_ip}")
        if 'proxy1_ip' in locals():
            print(f"代理1 IP (19965): {proxy1_ip}")
        if 'proxy2_ip' in locals():
            print(f"代理2 IP (19966): {proxy2_ip}")
        if 'final_ip' in locals():
            print(f"最终IP: {final_ip}")
        
        print("\n✨ 测试完成！")
        print("\n💡 优势:")
        print("1. ✅ 代理配置完全通过程序传递，扩展无硬编码")
        print("2. ✅ 支持任意代理服务器和端口")
        print("3. ✅ 用户名密码动态传递，更安全")
        print("4. ✅ 扩展ID固定，启动速度快")
        print("5. ✅ 可以同时管理多个不同的代理配置")
        
        print("\n🔧 使用方法:")
        print("```python")
        print("from dataimpulse_browser import UniversalProxyBrowser")
        print("")
        print("browser = UniversalProxyBrowser()")
        print("browser.add_proxy_config('my_proxy', 'host', port, 'name')")
        print("browser.switch_to_proxy('my_proxy', 'username', 'password')")
        print("browser.test_ip()")
        print("browser.disable_proxy()")
        print("```")
        
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        print("\n🔧 可能的原因:")
        print("1. 扩展文件夹不存在: dataimpulse_proxy_extension/")
        print("2. Chrome浏览器问题")
        print("3. 网络连接问题")
        print("4. 代理服务器问题")
        
    finally:
        try:
            browser.close()
            print("\n🔒 浏览器已关闭")
        except:
            pass


if __name__ == "__main__":
    main()
