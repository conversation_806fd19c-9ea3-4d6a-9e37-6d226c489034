"""
基础监控类模块
提供所有监控类的基础功能和结构
"""
import glob
import json
import logging
import os.path
import re
from abc import ABC, abstractmethod
from datetime import datetime
from urllib.parse import urlparse, urlunparse

import requests
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage, SessionOptions
from common.project_path import ProjectPaths
from utils.page_setting import configure_logger, load_cookies, random_sleep
from utils.proxy_setting import create_proxyauth_extension, set_switchy_omega
from utils.utils import load_toml


class Monitor(ABC):
    def _init_proxy(self) -> None:
        """
        初始化代理
        """
        # 品赞代理地址
        self.proxy_pin_zan_url = 'https://service.ipzan.com/core-extract?num=1&no=20250418922716594779&minute=1&pool=quality&secret=go1ouj73e1lf2h'

        # clash代理地址
        self.proxy_clash_url = {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}

        # ipcool代理地址
        self.ipcool_url = {
            'http': 'http://{}:{}@us.ipcool.net:2555'.format("13727744565_187_0_0_session_15_1", "lin2225427"),
            'https': 'http://{}:{}@us.ipcool.net:2555'.format("13727744565_187_0_0_session_15_1", "lin2225427")
        }

        allowed_values = {"clash", "pin_zan", "kuai_dai_li", "ipcool", None}
        if self.proxy_type and self.proxy_type not in allowed_values:
            raise ValueError("proxy_type must be one of 'clash', 'pin_zan', 'kuai_dai_li', 'ipcool'")

    def init_page(self) -> ChromiumPage:
        """
        初始化Chromium浏览器页面

        Returns:
            ChromiumPage: 配置好的浏览器页面对象
        """
        # 创建浏览器选项
        option = ChromiumOptions()

        # 静音浏览器
        option.mute(True)

        # 设置加载模式为eager以加快加载速度
        allowed_values = {"normal", "eager", "none", None}
        if self.load_mode and self.load_mode not in allowed_values:
            raise ValueError("load_mode 只允许在 {'normal', 'eager', 'none', None}")

        option.set_load_mode(self.load_mode)

        option.set_browser_path(r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe')

        # 禁用图片加载以提高性能
        option.no_imgs(self.is_no_img)

        # 根据配置决定是否使用无头模式
        option.headless(self.is_headless)

        # 根据配置决定是否自动切换端口
        if self.monitor_name != "julian" and self.is_auto_port:
            option.auto_port(self.is_auto_port)

        if self.monitor_name == "julian" or self.monitor_name == "mrporter":
            option.set_local_port(19999)

        option.ignore_certificate_errors(True)

        option.incognito(self.is_auto_port)

        # 设置代理
        if self.proxy_type:
            if self.proxy_type == "clash":
                option.set_proxy(self.proxy_clash_url['http'])
            elif self.proxy_type == "ipcool":
                option.add_extension(r"E:\pythonProject\extension\Proxy-SwitchyOmega-Chromium-2.5.20")

        # 设置用户代理头，确保格式正确
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        option.set_argument(f'--user-agent={user_agent}')

        # 创建并返回浏览器页面
        page = ChromiumPage(option)

        if self.proxy_type == "ipcool" and self.page:
            set_switchy_omega(page)

        self.logger.debug(f"{self.monitor_name} - 浏览器页面已初始化")
        return page

    def init_session(self) -> SessionPage:
        """
        初始化会话对象，用于发送HTTP请求

        Returns:
            SessionPage: 配置好的会话对象
        """
        session_option = SessionOptions()
        if self.proxy_type:
            if self.proxy_type == "clash":
                session_option.set_proxies(self.proxy_clash_url)

        return SessionPage(session_option)



import os
import string


def set_switchy_omega(page):
    """

    :param page:

    :return:
    """
    server_ip = 'us.ipcool.net'
    passport = '2555'
    username = "13727744565_187_0_0_session_5_1"
    password = "lin2225427"

    page.get("chrome-extension://fjplonlgeikcikbiffklcbdikcaienob/options.html#!/profile/proxy")

    page.ele('x://input[@ng-model="proxyEditors[scheme].host"]').clear().input(server_ip)
    page.ele('x://input[@ng-model="proxyEditors[scheme].port"]').clear().input(passport)

    page.ele('x://button[@ng-click="editProxyAuth(scheme)"]').click(by_js=True)

    page.ele('x://input[@ng-model="model"]').clear().click(by_js=True).input(username)
    page.ele('x://input[@ng-model="auth.password"]').clear().click(by_js=True).input(password)

    page.ele('x://button[@ng-disabled="!authForm.$valid"]').click(by_js=True)
    page.ele('x://a[@ng-click="applyOptions()"]').click(by_js=True)

    page.get('chrome-extension://fjplonlgeikcikbiffklcbdikcaienob/options.html#!/ui')

    page.ele('x://button[@class="btn btn-default dropdown-toggle"]').click(by_js=True)