#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SwitchyOmega无教程版本测试
验证源码修改效果
"""

import os
import time
import tempfile
from DrissionPage import ChromiumPage, ChromiumOptions


class SwitchyOmegaNoTutorial:
    """SwitchyOmega无教程版本"""
    
    def __init__(self):
        self.page = None
        self.temp_dir = None
        
        # 获取扩展路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        
        self.switchyomega_path = os.path.join(script_dir, "chromium-release")
        self.ua_patch_path = os.path.join(project_root, "cloudflare_ua_patch")
        
        # 正确的SwitchyOmega扩展ID（使用test111.py中的ID）
        self.extension_id = "fjplonlgeikcikbiffklcbdikcaienob"
        
        # 代理配置
        self.proxy_config = {
            "host": "gw.dataimpulse.com",
            "port": "19965", 
            "username": "0465846b31e1f583b17c__cr.us",
            "password": "16af34bf75f0573a"
        }
    
    def create_browser(self):
        """创建浏览器"""
        print("🚀 创建浏览器实例...")
        
        # 创建临时用户数据目录
        self.temp_dir = tempfile.mkdtemp(prefix="switchyomega_notutorial_")
        print(f"📁 用户数据目录: {self.temp_dir}")
        
        options = ChromiumOptions()
        options.set_paths(browser_path=r"C:\Users\<USER>\AppData\Local\Chromium\Application\chrome.exe")
        
        # 完全复制dpMitmdumpTest.py的启动参数
        args = [
            '--ignore-certificate-errors',
            '--no-first-run',
            '--force-color-profile=srgb',
            '--metrics-recording-only',
            '--password-store=basic',
            '--use-mock-keychain',
            '--export-tagged-pdf',
            '--no-default-browser-check',
            '--disable-background-mode',
            '--enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions',
            '--disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage',
            '--deny-permission-prompts',
            '--accept-lang=en-US',
            '--lang=en-US',
            '--accept-languages=en-US,en',
            '--window-size=1024,768',
            '--disable-extensions-file-access-check',
            '--disable-web-security'
        ]
        
        [options.set_argument(arg) for arg in args]
        
        # 设置用户数据目录
        options.set_user_data_path(self.temp_dir)
        
        # 加载扩展
        if os.path.exists(self.ua_patch_path):
            print(f"✅ 加载cloudflare_ua_patch: {self.ua_patch_path}")
            options.add_extension(self.ua_patch_path)
        
        if os.path.exists(self.switchyomega_path):
            print(f"✅ 加载SwitchyOmega: {self.switchyomega_path}")
            options.add_extension(self.switchyomega_path)
        else:
            print("❌ SwitchyOmega扩展未找到")
            return False
        
        options.set_argument('--remote-debugging-port=9222')  # 使用固定端口避免冲突
        
        try:
            self.page = ChromiumPage(options)
            print("✅ 浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def configure_proxy(self):
        """配置代理"""
        try:
            print("🔧 配置代理...")
            
            # 等待扩展加载
            print("⏳ 等待扩展加载...")
            time.sleep(3)  # 加快速度

            # 访问配置页面
            config_url = f"chrome-extension://{self.extension_id}/options.html#!/profile/proxy"
            print(f"📖 访问配置页面: {config_url}")
            self.page.get(config_url)
            time.sleep(2)  # 加快速度

            print("✅ 无教程弹窗！源码修改成功！")
            
            # 配置HTTPS代理
            print("🔧 配置HTTPS代理...")
            
            # 配置服务器地址
            host_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].host"]', timeout=5)
            if host_input:
                host_input.clear()
                host_input.input(self.proxy_config["host"])
                print(f"✅ 服务器: {self.proxy_config['host']}")
            
            # 配置端口
            port_input = self.page.ele('x://input[@ng-model="proxyEditors[scheme].port"]', timeout=5)
            if port_input:
                port_input.clear()
                port_input.input(self.proxy_config["port"])
                print(f"✅ 端口: {self.proxy_config['port']}")
            
            # 配置认证
            auth_button = self.page.ele('x://button[@ng-click="editProxyAuth(scheme)"]', timeout=3)
            if auth_button:
                auth_button.click(by_js=True)
                time.sleep(0.5)  # 加快速度

                # 用户名
                username_input = self.page.ele('x://input[@ng-model="model"]', timeout=3)
                if username_input:
                    username_input.clear()
                    username_input.click(by_js=True)
                    username_input.input(self.proxy_config["username"])
                    print("✅ 用户名已设置")

                # 密码
                password_input = self.page.ele('x://input[@ng-model="auth.password"]', timeout=3)
                if password_input:
                    password_input.clear()
                    password_input.click(by_js=True)
                    password_input.input(self.proxy_config["password"])
                    print("✅ 密码已设置")

                # 保存认证
                save_auth_button = self.page.ele('x://button[@ng-disabled="!authForm.$valid"]', timeout=3)
                if save_auth_button:
                    save_auth_button.click(by_js=True)
                    time.sleep(0.5)  # 加快速度
                    print("✅ 认证已保存")
            
            # 应用配置 - 关键步骤！等待按钮变成待保存状态
            print("🔧 等待配置变更检测...")

            # 等待按钮变成btn-success状态（绿色，表示有更改需要保存）
            apply_button = None
            for _ in range(10):  # 最多等待5秒
                # 更精确的选择器，定位到侧边栏的Apply按钮
                apply_button = self.page.ele('x://nav//a[@role="button" and @ng-click="applyOptions()" and contains(@class, "btn-success")]', timeout=0.5)
                if apply_button:
                    print("✅ 检测到配置更改，按钮已变绿")
                    break
                time.sleep(0.5)

            if apply_button:
                print("🔧 应用配置...")
                apply_button.click(by_js=True)
                time.sleep(1.5)  # 等待配置保存
                print("✅ 配置已应用并保存")
                return True
            else:
                # 尝试备用选择器
                print("🔍 尝试备用选择器...")
                apply_button = self.page.ele('x://a[contains(text(), "Apply changes") and contains(@class, "btn-success")]', timeout=2)
                if apply_button:
                    print("🔧 找到Apply按钮，点击应用...")
                    apply_button.click(by_js=True)
                    time.sleep(1.5)
                    print("✅ 配置已应用并保存")
                    return True
                else:
                    print("⚠️ 未找到Apply按钮或按钮未变绿")
                    return False
            
        except Exception as e:
            print(f"⚠️ 配置过程遇到问题: {e}")
            return False

    def activate_proxy_ui(self):
        """按照test111.py的方式在UI页面激活代理"""
        try:
            print("🔧 激活代理（UI页面方式）...")

            # 访问UI页面 - 按照test111.py的方式
            ui_url = f"chrome-extension://{self.extension_id}/options.html#!/ui"
            print(f"📖 访问UI页面: {ui_url}")
            self.page.get(ui_url)
            time.sleep(2)

            # 点击下拉菜单 - 按照test111.py的方式
            dropdown_btn = self.page.ele('x://button[@class="btn btn-default dropdown-toggle"]', timeout=5)
            if dropdown_btn:
                dropdown_btn.click(by_js=True)
                time.sleep(1)
                print("✅ 打开代理选择菜单")

                # 选择proxy配置 - 这是test111.py缺少的部分，我们补充
                proxy_option = self.page.ele('x://a[contains(text(), "proxy")]', timeout=5)
                if not proxy_option:
                    # 尝试其他选择器
                    proxy_option = self.page.ele('x://li[contains(text(), "proxy")]//a', timeout=3)
                if not proxy_option:
                    proxy_option = self.page.ele('x://*[contains(text(), "proxy")]', timeout=3)

                if proxy_option:
                    proxy_option.click(by_js=True)
                    time.sleep(2)
                    print("✅ 已选择proxy配置")
                    return True
                else:
                    print("⚠️ 未找到proxy选项")
                    # 打印可用选项用于调试
                    options = self.page.eles('x://ul[@class="dropdown-menu"]//a', timeout=3)
                    if options:
                        print("📋 可用选项:")
                        for i, option in enumerate(options):
                            text = option.text
                            print(f"  {i}: {text}")
                    return False
            else:
                print("⚠️ 未找到下拉菜单按钮")
                return False

        except Exception as e:
            print(f"⚠️ UI激活遇到问题: {e}")
            return False



    def test_proxy(self):
        """测试代理"""
        try:
            print("\n🔍 测试代理连接...")

            # 等待代理生效
            time.sleep(3)

            # 重新连接页面，避免连接断开问题
            try:
                # 测试IP
                self.page.get("https://api.ipify.org/")
                time.sleep(3)

                ip_content = self.page.html
                import re
                ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
                current_ip = ip_match.group() if ip_match else "未知"

                print(f"🌐 当前IP: {current_ip}")

                # 检查是否是代理IP
                if current_ip != "**************":
                    print("✅ 代理IP生效！")
                    return True
                else:
                    print("⚠️ 仍然是本地IP")
                    return False

            except Exception as conn_error:
                print(f"⚠️ 页面连接问题: {conn_error}")
                print("💡 这可能是因为代理激活导致的连接变化")
                print("🔄 尝试重新访问...")

                # 尝试重新访问
                try:
                    self.page.get("https://api.ipify.org/")
                    time.sleep(3)
                    ip_content = self.page.html
                    import re
                    ip_match = re.search(r'\d+\.\d+\.\d+\.\d+', ip_content)
                    current_ip = ip_match.group() if ip_match else "未知"
                    print(f"🌐 重试后IP: {current_ip}")
                    return current_ip != "**************"
                except:
                    print("⚠️ 重试失败，但代理可能已激活")
                    return False

        except Exception as e:
            print(f"⚠️ 测试遇到问题: {e}")
            return False
    
    def run(self):
        """运行测试"""
        print("🎮 SwitchyOmega无教程版本测试")
        print("🎯 验证源码修改效果")
        print()
        
        if not self.create_browser():
            return
        
        # 配置代理并保存
        if self.configure_proxy():
            print("\n✅ 代理配置并保存完成！")

            # 按照test111.py的方式激活代理
            if self.activate_proxy_ui():
                print("\n🎉 完美成功！")
                print("💡 源码修改效果:")
                print("  🚫 无教程弹窗")
                print("  🚫 无自动打开选项页面")
                print("  ✅ 代理配置已保存")
                print("  ✅ 代理已激活（按照test111.py方式）")

                # 测试代理
                if self.test_proxy():
                    print("\n🎉 代理测试成功！")
                    print("🌐 IP已切换到代理服务器")
                else:
                    print("\n⚠️ 代理可能未完全生效，但配置已完成")
            else:
                print("\n⚠️ 代理激活失败")
                print("💡 可以手动在浏览器中点击SwitchyOmega图标切换到proxy配置")
        else:
            print("\n❌ 代理配置失败")
        
        print("\n� 浏览器将保持打开状态，方便验证代理效果")
        print("🔍 请在浏览器中访问 https://api.ipify.org/ 验证IP变化")
        print("⏹️ 验证完成后可手动关闭浏览器")

        # 不自动关闭浏览器，让用户手动验证
        # if self.page:
        #     self.page.quit()
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            self.page.quit()


def main():
    """主函数"""
    try:
        test = SwitchyOmegaNoTutorial()
        test.run()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        # 不自动关闭浏览器，让用户手动验证代理效果
        # if 'test' in locals():
        #     test.close()
        pass


if __name__ == "__main__":
    main()
