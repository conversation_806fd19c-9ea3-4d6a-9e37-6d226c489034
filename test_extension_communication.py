"""
测试扩展通信 - 验证扩展是否能正确响应消息
"""

import time
import os
from DrissionPage import ChromiumPage, ChromiumOptions


def test_extension_communication():
    """测试扩展通信"""
    print("📡 扩展通信测试")
    print("=" * 40)
    
    extension_path = os.path.join(os.getcwd(), 'universal_proxy_extension')
    
    if not os.path.exists(extension_path):
        print(f"❌ 扩展文件夹不存在: {extension_path}")
        return False
    
    try:
        # 启动浏览器
        print("🚀 启动浏览器...")
        co = ChromiumOptions()
        co.add_extension(extension_path)
        co.set_argument('--disable-web-security')
        
        page = ChromiumPage(co)
        print("✅ 浏览器启动成功")
        
        # 等待扩展加载
        print("⏳ 等待扩展加载...")
        time.sleep(3)
        
        # 访问一个普通网页，然后尝试与扩展通信
        print("🌐 访问测试页面...")
        page.get('https://httpbin.org/ip')
        time.sleep(2)
        
        print("📡 尝试与扩展通信...")
        
        # 方法1: 尝试获取所有扩展ID
        try:
            print("🔍 方法1: 获取扩展列表...")
            extensions = page.run_js("""
                new Promise((resolve) => {
                    if (typeof chrome !== 'undefined' && chrome.management) {
                        chrome.management.getAll((extensions) => {
                            resolve(extensions.filter(ext => ext.type === 'extension'));
                        });
                    } else {
                        resolve([]);
                    }
                })
            """)
            
            print(f"📦 找到 {len(extensions)} 个扩展:")
            target_extension_id = None
            for ext in extensions:
                print(f"   - {ext.get('name', 'Unknown')}: {ext.get('id', 'Unknown ID')}")
                if 'Universal Proxy' in ext.get('name', '') or 'DataImpulse' in ext.get('name', ''):
                    target_extension_id = ext.get('id')
                    print(f"✅ 找到目标扩展: {target_extension_id}")
            
            if target_extension_id:
                print(f"\n📨 尝试向扩展发送ping消息...")
                result = page.run_js(f"""
                    new Promise((resolve, reject) => {{
                        const timeout = setTimeout(() => reject('timeout'), 5000);
                        chrome.runtime.sendMessage(
                            '{target_extension_id}',
                            {{ type: 'ping' }},
                            (response) => {{
                                clearTimeout(timeout);
                                if (chrome.runtime.lastError) {{
                                    reject(chrome.runtime.lastError.message);
                                }} else {{
                                    resolve(response);
                                }}
                            }}
                        );
                    }})
                """)
                
                if result:
                    print(f"✅ 扩展响应成功: {result}")
                    return True
                else:
                    print("❌ 扩展无响应")
            else:
                print("❌ 未找到目标扩展")
                
        except Exception as e:
            print(f"❌ 方法1失败: {e}")
        
        # 方法2: 尝试直接通过扩展页面通信
        try:
            print("\n🔍 方法2: 访问扩展popup页面...")
            
            # 先获取扩展ID（通过访问扩展管理页面）
            page.get('chrome://extensions/')
            time.sleep(2)
            
            # 查找扩展ID
            extension_cards = page.run_js("""
                Array.from(document.querySelectorAll('extensions-item')).map(item => {
                    const name = item.shadowRoot?.querySelector('#name')?.textContent || '';
                    const id = item.getAttribute('id') || '';
                    return { name, id };
                }).filter(item => item.name.includes('Universal') || item.name.includes('DataImpulse'));
            """)
            
            if extension_cards and len(extension_cards) > 0:
                ext_id = extension_cards[0]['id']
                print(f"✅ 从管理页面获取扩展ID: {ext_id}")
                
                # 访问扩展popup页面
                popup_url = f"chrome-extension://{ext_id}/popup.html"
                print(f"🔗 访问popup页面: {popup_url}")
                page.get(popup_url)
                time.sleep(2)
                
                # 在popup页面中测试扩展API
                result = page.run_js("""
                    new Promise((resolve, reject) => {
                        if (typeof chrome !== 'undefined' && chrome.runtime) {
                            chrome.runtime.sendMessage(
                                { type: 'ping' },
                                (response) => {
                                    if (chrome.runtime.lastError) {
                                        reject(chrome.runtime.lastError.message);
                                    } else {
                                        resolve(response);
                                    }
                                }
                            );
                        } else {
                            reject('Chrome runtime not available');
                        }
                    })
                """)
                
                if result:
                    print(f"✅ 通过popup页面通信成功: {result}")
                    return True
                else:
                    print("❌ popup页面通信失败")
            else:
                print("❌ 未在管理页面找到扩展")
                
        except Exception as e:
            print(f"❌ 方法2失败: {e}")
        
        print("❌ 所有通信方法都失败了")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        try:
            page.close()
            print("\n🔒 浏览器已关闭")
        except:
            pass


if __name__ == "__main__":
    success = test_extension_communication()
    if success:
        print("\n🎉 扩展通信测试成功！")
        print("✅ 扩展已正确加载并能响应消息")
    else:
        print("\n💥 扩展通信测试失败！")
        print("\n🔧 可能的原因:")
        print("1. 扩展权限配置问题")
        print("2. 消息监听器未正确设置")
        print("3. 扩展上下文问题")
        print("4. Chrome版本兼容性问题")
