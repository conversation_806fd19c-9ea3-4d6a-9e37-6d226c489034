"""
DataImpulse代理浏览器 - 专门适配DataImpulse代理服务的快速浏览器
支持固定扩展ID、快速代理切换和IP测试

使用方法:
    browser = DataImpulseBrowser("your_username", "your_password")
    browser.test_ip()  # 测试当前IP
    browser.switch_to_proxy1()  # 切换到代理1
    browser.switch_to_proxy2()  # 切换到代理2
    browser.disable_proxy()  # 禁用代理
"""

import time
import os
from DrissionPage import ChromiumPage, ChromiumOptions
import json


class DataImpulseBrowser:
    """
    DataImpulse专用代理浏览器
    - 固定扩展ID（基于固定文件夹路径）
    - 预配置DataImpulse代理服务器
    - 快速代理切换（0.5-1秒）
    - 内置IP测试功能
    """
    
    # 固定的扩展路径（确保ID固定）
    EXTENSION_PATH = os.path.join(os.getcwd(), 'dataimpulse_proxy_extension')
    
    # DataImpulse代理配置
    PROXY_CONFIGS = {
        'proxy1': {
            'name': 'DataImpulse Proxy 1',
            'host': 'gw.dataimpulse.com',
            'port': 19965,
            'key': 'proxy1'
        },
        'proxy2': {
            'name': 'DataImpulse Proxy 2',
            'host': 'gw.dataimpulse.com', 
            'port': 19966,
            'key': 'proxy2'
        }
    }
    
    def __init__(self, username, password, browser_id=None):
        """
        初始化DataImpulse浏览器
        
        Args:
            username (str): DataImpulse用户名
            password (str): DataImpulse密码
            browser_id (str, optional): 浏览器标识符
        """
        self.username = username
        self.password = password
        self.browser_id = browser_id or f"dataimpulse_{int(time.time())}"
        self.page = None
        self.extension_id = None
        self.current_proxy = None
        
        print(f"🚀 初始化DataImpulse浏览器 {self.browser_id}")
        self._setup_browser()
    
    def _setup_browser(self):
        """设置浏览器和扩展"""
        start_time = time.time()
        
        # 检查扩展是否存在
        if not os.path.exists(self.EXTENSION_PATH):
            raise FileNotFoundError(f"扩展文件夹不存在: {self.EXTENSION_PATH}")
        
        # 配置浏览器选项
        co = ChromiumOptions()
        co.add_extension(path=self.EXTENSION_PATH)
        co.add_argument('--disable-web-security')
        co.add_argument('--disable-dev-shm-usage')
        co.add_argument('--no-sandbox')
        
        # 启动浏览器
        self.page = ChromiumPage(co)
        
        # 等待扩展加载
        time.sleep(2)
        
        # 获取扩展ID（通过ping测试）
        self._get_extension_id()
        
        elapsed = time.time() - start_time
        print(f"✅ 浏览器 {self.browser_id} 启动完成，耗时: {elapsed:.2f}秒")
        print(f"🎯 扩展ID: {self.extension_id}")
    
    def _get_extension_id(self):
        """获取扩展ID"""
        try:
            result = self.page.run_js("""
                new Promise((resolve, reject) => {
                    // 尝试ping扩展获取ID
                    const timeout = setTimeout(() => {
                        reject('Extension not responding');
                    }, 3000);
                    
                    // 由于扩展路径固定，ID也是固定的
                    // 这里通过ping来验证扩展是否正常工作
                    chrome.runtime.sendMessage(
                        chrome.runtime.id,  // 发送给自己
                        { type: 'ping' },
                        function(response) {
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {
                                reject('Extension error: ' + chrome.runtime.lastError.message);
                            } else {
                                resolve(response);
                            }
                        }
                    );
                })
            """)
            
            if result and result.get('success'):
                self.extension_id = result.get('extensionId', 'unknown')
                print(f"✅ 扩展连接成功，ID: {self.extension_id}")
            else:
                print("⚠️ 扩展ping失败，使用备用方法")
                self.extension_id = "dataimpulse-extension"
                
        except Exception as e:
            print(f"⚠️ 获取扩展ID失败: {e}")
            self.extension_id = "dataimpulse-extension"
    
    def _send_message_to_extension(self, message):
        """向扩展发送消息"""
        try:
            result = self.page.run_js(f"""
                new Promise((resolve, reject) => {{
                    const timeout = setTimeout(() => {{
                        reject('Extension timeout');
                    }}, 5000);
                    
                    chrome.runtime.sendMessage(
                        '{self.extension_id}',
                        {json.dumps(message)},
                        function(response) {{
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {{
                                reject('Extension error: ' + chrome.runtime.lastError.message);
                            }} else {{
                                resolve(response);
                            }}
                        }}
                    );
                }})
            """)
            return result
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_ip(self):
        """测试当前IP地址"""
        print(f"🌐 [{self.browser_id}] 测试当前IP...")
        start_time = time.time()
        
        try:
            # 方法1：通过扩展测试
            result = self._send_message_to_extension({'type': 'testIP'})
            
            if result and result.get('success'):
                elapsed = time.time() - start_time
                ip = result.get('ip', 'Unknown')
                print(f"✅ [{self.browser_id}] 当前IP: {ip}，耗时: {elapsed:.2f}秒")
                return ip
            
            # 方法2：直接访问API
            print("🔄 使用备用方法测试IP...")
            self.page.get('https://api.ipify.org?format=json', timeout=8)
            ip_data = self.page.json
            ip = ip_data.get('ip', 'Unknown') if ip_data else 'Unknown'
            
            elapsed = time.time() - start_time
            print(f"✅ [{self.browser_id}] 当前IP: {ip}，耗时: {elapsed:.2f}秒")
            return ip
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ [{self.browser_id}] IP测试失败，耗时: {elapsed:.2f}秒，错误: {e}")
            return None
    
    def switch_to_proxy1(self):
        """切换到DataImpulse代理1 (19965)"""
        return self._switch_proxy('proxy1')
    
    def switch_to_proxy2(self):
        """切换到DataImpulse代理2 (19966)"""
        return self._switch_proxy('proxy2')
    
    def _switch_proxy(self, proxy_key):
        """内部代理切换方法"""
        proxy_config = self.PROXY_CONFIGS.get(proxy_key)
        if not proxy_config:
            print(f"❌ 未知的代理配置: {proxy_key}")
            return False
        
        print(f"⚡ [{self.browser_id}] 切换到 {proxy_config['name']}")
        start_time = time.time()
        
        try:
            result = self._send_message_to_extension({
                'type': 'switchDataImpulse',
                'proxyKey': proxy_key,
                'username': self.username,
                'password': self.password
            })
            
            elapsed = time.time() - start_time
            
            if result and result.get('success'):
                self.current_proxy = proxy_key
                print(f"✅ [{self.browser_id}] {result.get('message')}，耗时: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ [{self.browser_id}] 代理切换失败，耗时: {elapsed:.2f}秒")
                print(f"   错误: {result.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"💥 [{self.browser_id}] 代理切换异常，耗时: {elapsed:.2f}秒，错误: {e}")
            return False
    
    def disable_proxy(self):
        """禁用代理"""
        print(f"🚫 [{self.browser_id}] 禁用代理...")
        start_time = time.time()
        
        try:
            result = self._send_message_to_extension({'type': 'disableProxy'})
            
            elapsed = time.time() - start_time
            
            if result and result.get('success'):
                self.current_proxy = None
                print(f"✅ [{self.browser_id}] 代理已禁用，耗时: {elapsed:.2f}秒")
                return True
            else:
                print(f"❌ [{self.browser_id}] 禁用代理失败，耗时: {elapsed:.2f}秒")
                return False
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"💥 [{self.browser_id}] 禁用代理异常，耗时: {elapsed:.2f}秒，错误: {e}")
            return False
    
    def get(self, url, **kwargs):
        """访问网页"""
        return self.page.get(url, **kwargs)
    
    def close(self):
        """关闭浏览器"""
        if self.page:
            print(f"🔒 关闭浏览器 {self.browser_id}")
            self.page.quit()


# 使用示例
if __name__ == "__main__":
    # 请替换为你的DataImpulse凭据
    USERNAME = "your_username"
    PASSWORD = "your_password"
    
    print("🚀 DataImpulse代理浏览器测试")
    
    # 创建浏览器实例
    browser = DataImpulseBrowser(USERNAME, PASSWORD)
    
    try:
        # 测试当前IP
        print("\n1. 测试初始IP:")
        browser.test_ip()
        
        # 切换到代理1
        print("\n2. 切换到代理1:")
        browser.switch_to_proxy1()
        time.sleep(2)
        browser.test_ip()
        
        # 切换到代理2
        print("\n3. 切换到代理2:")
        browser.switch_to_proxy2()
        time.sleep(2)
        browser.test_ip()
        
        # 禁用代理
        print("\n4. 禁用代理:")
        browser.disable_proxy()
        time.sleep(2)
        browser.test_ip()
        
        print("\n✨ 测试完成！")
        
    finally:
        browser.close()
